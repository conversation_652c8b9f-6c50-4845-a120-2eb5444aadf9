{"name": "@next-auth/supabase-adapter", "version": "0.2.1", "description": "Supabase adapter for next-auth.", "homepage": "https://authjs.dev", "repository": "https://github.com/nextauthjs/next-auth", "bugs": {"url": "https://github.com/nextauthjs/next-auth/issues"}, "author": "<PERSON> <<EMAIL>>", "main": "dist/index.js", "keywords": ["next-auth", "next.js", "supabase"], "license": "ISC", "private": false, "publishConfig": {"access": "public"}, "peerDependencies": {"@supabase/supabase-js": "^2.0.5", "next-auth": "^4.18.7"}, "devDependencies": {"@next-auth/adapter-test": "^0.0.0", "@next-auth/tsconfig": "^0.0.0", "@supabase/supabase-js": "^2.0.5", "jest": "^27.4.3", "next-auth": "4.18.6"}, "jest": {"preset": "@next-auth/adapter-test/jest"}, "scripts": {"build": "tsc", "test": "./tests/test.sh"}}