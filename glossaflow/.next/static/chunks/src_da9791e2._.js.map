{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Sheet = SheetPrimitive.Root\n\nconst SheetTrigger = SheetPrimitive.Trigger\n\nconst SheetClose = SheetPrimitive.Close\n\nconst SheetPortal = SheetPrimitive.Portal\n\nconst SheetOverlay = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\n\nconst sheetVariants = cva(\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n  {\n    variants: {\n      side: {\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n        bottom:\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n        right:\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\n      },\n    },\n    defaultVariants: {\n      side: \"right\",\n    },\n  }\n)\n\ninterface SheetContentProps\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\n    VariantProps<typeof sheetVariants> {}\n\nconst SheetContent = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Content>,\n  SheetContentProps\n>(({ side = \"right\", className, children, ...props }, ref) => (\n  <SheetPortal>\n    <SheetOverlay />\n    <SheetPrimitive.Content\n      ref={ref}\n      className={cn(sheetVariants({ side }), className)}\n      {...props}\n    >\n      {children}\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </SheetPrimitive.Close>\n    </SheetPrimitive.Content>\n  </SheetPortal>\n))\nSheetContent.displayName = SheetPrimitive.Content.displayName\n\nconst SheetHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetHeader.displayName = \"SheetHeader\"\n\nconst SheetFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetFooter.displayName = \"SheetFooter\"\n\nconst SheetTitle = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\n    {...props}\n  />\n))\nSheetTitle.displayName = SheetPrimitive.Title.displayName\n\nconst SheetDescription = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nSheetDescription.displayName = SheetPrimitive.Description.displayName\n\nexport {\n  Sheet,\n  SheetPortal,\n  SheetOverlay,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,QAAQ,qKAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,qKAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,qKAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,qKAAA,CAAA,SAAqB;AAEzC,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gLACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,aAAa,WAAW,GAAG,qKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGlC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,qKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,qKAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,qKAAA,CAAA,cAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/accessibility/SkipLink.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface SkipLinkProps {\n  href: string;\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function SkipLink({ href, children, className = '' }: SkipLinkProps) {\n  const [isVisible, setIsVisible] = useState(false);\n\n  return (\n    <a\n      href={href}\n      className={`\n        fixed top-4 left-4 z-50 px-4 py-2 bg-blue-600 text-white rounded-md\n        transform transition-transform duration-200 ease-in-out\n        focus:translate-y-0 focus:opacity-100\n        ${isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'}\n        ${className}\n      `}\n      onFocus={() => setIsVisible(true)}\n      onBlur={() => setIsVisible(false)}\n    >\n      {children}\n    </a>\n  );\n}\n\nexport function SkipLinks() {\n  return (\n    <>\n      <SkipLink href=\"#main-content\">Skip to main content</SkipLink>\n      <SkipLink href=\"#navigation\">Skip to navigation</SkipLink>\n      <SkipLink href=\"#search\">Skip to search</SkipLink>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAUO,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAiB;;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAC;;;;QAIV,EAAE,YAAY,8BAA8B,8BAA8B;QAC1E,EAAE,UAAU;MACd,CAAC;QACD,SAAS,IAAM,aAAa;QAC5B,QAAQ,IAAM,aAAa;kBAE1B;;;;;;AAGP;GAnBgB;KAAA;AAqBT,SAAS;IACd,qBACE;;0BACE,6LAAC;gBAAS,MAAK;0BAAgB;;;;;;0BAC/B,6LAAC;gBAAS,MAAK;0BAAc;;;;;;0BAC7B,6LAAC;gBAAS,MAAK;0BAAU;;;;;;;;AAG/B;MARgB", "debugId": null}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/accessibility/ScreenReaderOnly.tsx"], "sourcesContent": ["interface ScreenReaderOnlyProps {\n  children: React.ReactNode;\n  as?: keyof JSX.IntrinsicElements;\n  className?: string;\n}\n\nexport function ScreenReaderOnly({ \n  children, \n  as: Component = 'span',\n  className = '' \n}: ScreenReaderOnlyProps) {\n  return (\n    <Component \n      className={`sr-only ${className}`}\n      aria-hidden=\"false\"\n    >\n      {children}\n    </Component>\n  );\n}\n\n// Utility component for live regions\ninterface LiveRegionProps {\n  children: React.ReactNode;\n  priority?: 'polite' | 'assertive';\n  atomic?: boolean;\n  relevant?: 'additions' | 'removals' | 'text' | 'all';\n}\n\nexport function LiveRegion({ \n  children, \n  priority = 'polite',\n  atomic = true,\n  relevant = 'all'\n}: LiveRegionProps) {\n  return (\n    <div\n      aria-live={priority}\n      aria-atomic={atomic}\n      aria-relevant={relevant}\n      className=\"sr-only\"\n    >\n      {children}\n    </div>\n  );\n}\n\n// Component for status messages\ninterface StatusMessageProps {\n  message: string;\n  type?: 'success' | 'error' | 'warning' | 'info';\n  visible?: boolean;\n}\n\nexport function StatusMessage({ \n  message, \n  type = 'info',\n  visible = true \n}: StatusMessageProps) {\n  if (!visible) return null;\n\n  const priority = type === 'error' ? 'assertive' : 'polite';\n\n  return (\n    <LiveRegion priority={priority}>\n      <span role=\"status\" aria-label={`${type}: ${message}`}>\n        {message}\n      </span>\n    </LiveRegion>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAMO,SAAS,iBAAiB,EAC/B,QAAQ,EACR,IAAI,YAAY,MAAM,EACtB,YAAY,EAAE,EACQ;IACtB,qBACE,6LAAC;QACC,WAAW,CAAC,QAAQ,EAAE,WAAW;QACjC,eAAY;kBAEX;;;;;;AAGP;KAbgB;AAuBT,SAAS,WAAW,EACzB,QAAQ,EACR,WAAW,QAAQ,EACnB,SAAS,IAAI,EACb,WAAW,KAAK,EACA;IAChB,qBACE,6LAAC;QACC,aAAW;QACX,eAAa;QACb,iBAAe;QACf,WAAU;kBAET;;;;;;AAGP;MAhBgB;AAyBT,SAAS,cAAc,EAC5B,OAAO,EACP,OAAO,MAAM,EACb,UAAU,IAAI,EACK;IACnB,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,WAAW,SAAS,UAAU,cAAc;IAElD,qBACE,6LAAC;QAAW,UAAU;kBACpB,cAAA,6LAAC;YAAK,MAAK;YAAS,cAAY,GAAG,KAAK,EAAE,EAAE,SAAS;sBAClD;;;;;;;;;;;AAIT;MAhBgB", "debugId": null}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useSession, signOut } from 'next-auth/react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';\nimport { SkipLinks } from '@/components/accessibility/SkipLink';\nimport { ScreenReaderOnly } from '@/components/accessibility/ScreenReaderOnly';\nimport {\n  Home,\n  FolderOpen,\n  BookOpen,\n  Users,\n  Settings,\n  LogOut,\n  Menu,\n  Bell,\n  Search,\n  Plus,\n} from 'lucide-react';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: Home },\n  { name: 'Projects', href: '/dashboard/projects', icon: FolderOpen },\n  { name: 'Terminology', href: '/dashboard/terminology', icon: BookOpen },\n  { name: 'Team', href: '/dashboard/team', icon: Users },\n  { name: 'Settings', href: '/dashboard/settings', icon: Settings },\n];\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const { data: session } = useSession();\n  const pathname = usePathname();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <SkipLinks />\n\n      {/* Mobile sidebar */}\n      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>\n        <SheetContent side=\"left\" className=\"w-64 p-0\">\n          <div className=\"flex h-full flex-col\">\n            <div className=\"flex h-16 items-center px-6 border-b\">\n              <Link href=\"/dashboard\" className=\"flex items-center\">\n                <span className=\"text-xl font-bold text-blue-600\">GlossaFlow</span>\n                <ScreenReaderOnly>- Translation Management Platform</ScreenReaderOnly>\n              </Link>\n            </div>\n            <nav\n              className=\"flex-1 space-y-1 px-3 py-4\"\n              id=\"navigation\"\n              role=\"navigation\"\n              aria-label=\"Main navigation\"\n            >\n              {navigation.map((item) => {\n                const isActive = pathname === item.href;\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-blue-100 text-blue-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    }`}\n                    onClick={() => setSidebarOpen(false)}\n                    aria-current={isActive ? 'page' : undefined}\n                  >\n                    <item.icon className=\"mr-3 h-5 w-5\" aria-hidden=\"true\" />\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n        </SheetContent>\n      </Sheet>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex min-h-0 flex-1 flex-col bg-white border-r border-gray-200\">\n          <div className=\"flex h-16 items-center px-6 border-b\">\n            <Link href=\"/dashboard\" className=\"flex items-center\">\n              <span className=\"text-xl font-bold text-blue-600\">GlossaFlow</span>\n            </Link>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-3 py-4\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                    isActive\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-700 hover:bg-gray-100'\n                  }`}\n                >\n                  <item.icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top navigation */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <SheetTrigger asChild>\n            <Button variant=\"ghost\" size=\"sm\" className=\"lg:hidden\">\n              <Menu className=\"h-5 w-5\" />\n            </Button>\n          </SheetTrigger>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1 items-center\">\n              <Search className=\"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 pl-3\" />\n              <input\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm\"\n                placeholder=\"Search projects, terminology...\"\n                type=\"search\"\n              />\n            </div>\n            <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n              <Button size=\"sm\" className=\"hidden sm:flex\">\n                <Plus className=\"mr-2 h-4 w-4\" />\n                New Project\n              </Button>\n\n              <Button variant=\"ghost\" size=\"sm\">\n                <Bell className=\"h-5 w-5\" />\n              </Button>\n\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                    <Avatar className=\"h-8 w-8\">\n                      <AvatarImage src={session?.user?.image || ''} alt={session?.user?.name || ''} />\n                      <AvatarFallback>\n                        {session?.user?.name?.charAt(0) || session?.user?.email?.charAt(0) || 'U'}\n                      </AvatarFallback>\n                    </Avatar>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                  <DropdownMenuLabel className=\"font-normal\">\n                    <div className=\"flex flex-col space-y-1\">\n                      <p className=\"text-sm font-medium leading-none\">{session?.user?.name}</p>\n                      <p className=\"text-xs leading-none text-muted-foreground\">\n                        {session?.user?.email}\n                      </p>\n                    </div>\n                  </DropdownMenuLabel>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard/profile\">Profile</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard/settings\">Settings</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem\n                    className=\"text-red-600\"\n                    onClick={() => signOut({ callbackUrl: '/auth/signin' })}\n                  >\n                    <LogOut className=\"mr-2 h-4 w-4\" />\n                    Sign out\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main\n          className=\"py-6\"\n          id=\"main-content\"\n          role=\"main\"\n          aria-label=\"Main content\"\n        >\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAnBA;;;;;;;;;;;;AAoCA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,sMAAA,CAAA,OAAI;IAAC;IACpD;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,qNAAA,CAAA,aAAU;IAAC;IAClE;QAAE,MAAM;QAAe,MAAM;QAA0B,MAAM,iNAAA,CAAA,WAAQ;IAAC;IACtE;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,uMAAA,CAAA,QAAK;IAAC;IACrD;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,6MAAA,CAAA,WAAQ;IAAC;CACjE;AAEM,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IAChE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,kJAAA,CAAA,YAAS;;;;;0BAGV,6LAAC,oIAAA,CAAA,QAAK;gBAAC,MAAM;gBAAa,cAAc;0BACtC,cAAA,6LAAC,oIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAO,WAAU;8BAClC,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;;sDAChC,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;sDAClD,6LAAC,0JAAA,CAAA,mBAAgB;sDAAC;;;;;;;;;;;;;;;;;0CAGtB,6LAAC;gCACC,WAAU;gCACV,IAAG;gCACH,MAAK;gCACL,cAAW;0CAEV,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,8BACA,mCACJ;wCACF,SAAS,IAAM,eAAe;wCAC9B,gBAAc,WAAW,SAAS;;0DAElC,6LAAC,KAAK,IAAI;gDAAC,WAAU;gDAAe,eAAY;;;;;;4CAC/C,KAAK,IAAI;;uCAXL,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;;;;;;;;;;;;0BAOR,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAChC,cAAA,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;sCAGtD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,8BACA,mCACJ;;sDAEF,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCATL,KAAK,IAAI;;;;;4BAYpB;;;;;;;;;;;;;;;;;0BAMN,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;8CAC1C,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAIpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDACC,WAAU;gDACV,aAAY;gDACZ,MAAK;;;;;;;;;;;;kDAGT,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,WAAU;;kEAC1B,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAInC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;0DAC3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAGlB,6LAAC,+IAAA,CAAA,eAAY;;kEACX,6LAAC,+IAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,WAAU;sEAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;;kFAChB,6LAAC,qIAAA,CAAA,cAAW;wEAAC,KAAK,SAAS,MAAM,SAAS;wEAAI,KAAK,SAAS,MAAM,QAAQ;;;;;;kFAC1E,6LAAC,qIAAA,CAAA,iBAAc;kFACZ,SAAS,MAAM,MAAM,OAAO,MAAM,SAAS,MAAM,OAAO,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;kEAK9E,6LAAC,+IAAA,CAAA,sBAAmB;wDAAC,WAAU;wDAAO,OAAM;wDAAM,UAAU;;0EAC1D,6LAAC,+IAAA,CAAA,oBAAiB;gEAAC,WAAU;0EAC3B,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAoC,SAAS,MAAM;;;;;;sFAChE,6LAAC;4EAAE,WAAU;sFACV,SAAS,MAAM;;;;;;;;;;;;;;;;;0EAItB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0EACtB,6LAAC,+IAAA,CAAA,mBAAgB;gEAAC,OAAO;0EACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;8EAAqB;;;;;;;;;;;0EAElC,6LAAC,+IAAA,CAAA,mBAAgB;gEAAC,OAAO;0EACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;8EAAsB;;;;;;;;;;;0EAEnC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0EACtB,6LAAC,+IAAA,CAAA,mBAAgB;gEACf,WAAU;gEACV,SAAS,IAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;wEAAE,aAAa;oEAAe;;kFAErD,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU/C,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,MAAK;wBACL,cAAW;kCAEX,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAnKgB;;QACY,iJAAA,CAAA,aAAU;QACnB,qIAAA,CAAA,cAAW;;;KAFd", "debugId": null}}, {"offset": {"line": 1304, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 1419, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1451, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1503, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/terminology/TerminologyTable.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { \n  MoreHorizontal, \n  Edit, \n  Trash2, \n  Check, \n  X,\n  Eye,\n  Clock\n} from 'lucide-react';\n\ninterface TerminologyEntry {\n  id: string;\n  sourceTerm: string;\n  targetTerm: string;\n  targetLanguage: string;\n  category: string;\n  context: string;\n  usageNotes: string;\n  approvalStatus: 'approved' | 'pending' | 'rejected';\n  frequency: number;\n  createdBy: string;\n  reviewedBy: string | null;\n  lastUsed: string;\n  createdAt: string;\n}\n\ninterface TerminologyTableProps {\n  terminology: TerminologyEntry[];\n  onEdit: (term: TerminologyEntry) => void;\n  onDelete: (termId: string) => void;\n  onApprove: (termId: string) => void;\n  onReject: (termId: string) => void;\n}\n\nconst statusColors = {\n  approved: 'bg-green-100 text-green-800',\n  pending: 'bg-yellow-100 text-yellow-800',\n  rejected: 'bg-red-100 text-red-800',\n};\n\nconst statusIcons = {\n  approved: Check,\n  pending: Clock,\n  rejected: X,\n};\n\nconst categoryColors = {\n  technical: 'bg-blue-100 text-blue-800',\n  general: 'bg-gray-100 text-gray-800',\n  character: 'bg-purple-100 text-purple-800',\n  location: 'bg-green-100 text-green-800',\n  concept: 'bg-orange-100 text-orange-800',\n};\n\nexport function TerminologyTable({ \n  terminology, \n  onEdit, \n  onDelete, \n  onApprove, \n  onReject \n}: TerminologyTableProps) {\n  const [selectedTerms, setSelectedTerms] = useState<string[]>([]);\n\n  if (terminology.length === 0) {\n    return (\n      <Card>\n        <CardContent className=\"p-12 text-center\">\n          <div className=\"text-gray-400 mb-4\">\n            <Eye className=\"h-12 w-12 mx-auto\" />\n          </div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No terminology found</h3>\n          <p className=\"text-gray-600\">\n            No terms match your current filters. Try adjusting your search criteria.\n          </p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>Terminology Entries ({terminology.length})</CardTitle>\n      </CardHeader>\n      <CardContent>\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead>\n              <tr className=\"border-b\">\n                <th className=\"text-left py-3 px-4 font-medium text-gray-900\">Source Term</th>\n                <th className=\"text-left py-3 px-4 font-medium text-gray-900\">Target Term</th>\n                <th className=\"text-left py-3 px-4 font-medium text-gray-900\">Language</th>\n                <th className=\"text-left py-3 px-4 font-medium text-gray-900\">Category</th>\n                <th className=\"text-left py-3 px-4 font-medium text-gray-900\">Status</th>\n                <th className=\"text-left py-3 px-4 font-medium text-gray-900\">Usage</th>\n                <th className=\"text-left py-3 px-4 font-medium text-gray-900\">Created By</th>\n                <th className=\"text-right py-3 px-4 font-medium text-gray-900\">Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {terminology.map((term) => {\n                const StatusIcon = statusIcons[term.approvalStatus];\n                return (\n                  <tr key={term.id} className=\"border-b hover:bg-gray-50\">\n                    <td className=\"py-4 px-4\">\n                      <div>\n                        <div className=\"font-medium text-gray-900\">{term.sourceTerm}</div>\n                        {term.context && (\n                          <div className=\"text-sm text-gray-500 mt-1\">{term.context}</div>\n                        )}\n                      </div>\n                    </td>\n                    <td className=\"py-4 px-4\">\n                      <div className=\"font-medium text-gray-900\">{term.targetTerm}</div>\n                      {term.usageNotes && (\n                        <div className=\"text-sm text-gray-500 mt-1\">{term.usageNotes}</div>\n                      )}\n                    </td>\n                    <td className=\"py-4 px-4\">\n                      <span className=\"text-sm text-gray-900\">{term.targetLanguage}</span>\n                    </td>\n                    <td className=\"py-4 px-4\">\n                      <Badge className={categoryColors[term.category as keyof typeof categoryColors]}>\n                        {term.category}\n                      </Badge>\n                    </td>\n                    <td className=\"py-4 px-4\">\n                      <Badge className={statusColors[term.approvalStatus]}>\n                        <StatusIcon className=\"mr-1 h-3 w-3\" />\n                        {term.approvalStatus}\n                      </Badge>\n                    </td>\n                    <td className=\"py-4 px-4\">\n                      <div className=\"text-sm\">\n                        <div className=\"font-medium\">{term.frequency} times</div>\n                        <div className=\"text-gray-500\">Last: {term.lastUsed}</div>\n                      </div>\n                    </td>\n                    <td className=\"py-4 px-4\">\n                      <div className=\"text-sm\">\n                        <div className=\"font-medium\">{term.createdBy}</div>\n                        <div className=\"text-gray-500\">{term.createdAt}</div>\n                      </div>\n                    </td>\n                    <td className=\"py-4 px-4 text-right\">\n                      <DropdownMenu>\n                        <DropdownMenuTrigger asChild>\n                          <Button variant=\"ghost\" size=\"sm\">\n                            <MoreHorizontal className=\"h-4 w-4\" />\n                          </Button>\n                        </DropdownMenuTrigger>\n                        <DropdownMenuContent align=\"end\">\n                          <DropdownMenuItem onClick={() => onEdit(term)}>\n                            <Edit className=\"mr-2 h-4 w-4\" />\n                            Edit\n                          </DropdownMenuItem>\n                          {term.approvalStatus === 'pending' && (\n                            <>\n                              <DropdownMenuItem onClick={() => onApprove(term.id)}>\n                                <Check className=\"mr-2 h-4 w-4\" />\n                                Approve\n                              </DropdownMenuItem>\n                              <DropdownMenuItem onClick={() => onReject(term.id)}>\n                                <X className=\"mr-2 h-4 w-4\" />\n                                Reject\n                              </DropdownMenuItem>\n                            </>\n                          )}\n                          <DropdownMenuItem \n                            onClick={() => onDelete(term.id)}\n                            className=\"text-red-600\"\n                          >\n                            <Trash2 className=\"mr-2 h-4 w-4\" />\n                            Delete\n                          </DropdownMenuItem>\n                        </DropdownMenuContent>\n                      </DropdownMenu>\n                    </td>\n                  </tr>\n                );\n              })}\n            </tbody>\n          </table>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAZA;;;;;;;AA8CA,MAAM,eAAe;IACnB,UAAU;IACV,SAAS;IACT,UAAU;AACZ;AAEA,MAAM,cAAc;IAClB,UAAU,uMAAA,CAAA,QAAK;IACf,SAAS,uMAAA,CAAA,QAAK;IACd,UAAU,+LAAA,CAAA,IAAC;AACb;AAEA,MAAM,iBAAiB;IACrB,WAAW;IACX,SAAS;IACT,WAAW;IACX,UAAU;IACV,SAAS;AACX;AAEO,SAAS,iBAAiB,EAC/B,WAAW,EACX,MAAM,EACN,QAAQ,EACR,SAAS,EACT,QAAQ,EACc;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,IAAI,YAAY,MAAM,KAAK,GAAG;QAC5B,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;kCAEjB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAMrC;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;;wBAAC;wBAAsB,YAAY,MAAM;wBAAC;;;;;;;;;;;;0BAEtD,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;0CACC,cAAA,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAU;sDAAgD;;;;;;sDAC9D,6LAAC;4CAAG,WAAU;sDAAgD;;;;;;sDAC9D,6LAAC;4CAAG,WAAU;sDAAgD;;;;;;sDAC9D,6LAAC;4CAAG,WAAU;sDAAgD;;;;;;sDAC9D,6LAAC;4CAAG,WAAU;sDAAgD;;;;;;sDAC9D,6LAAC;4CAAG,WAAU;sDAAgD;;;;;;sDAC9D,6LAAC;4CAAG,WAAU;sDAAgD;;;;;;sDAC9D,6LAAC;4CAAG,WAAU;sDAAiD;;;;;;;;;;;;;;;;;0CAGnE,6LAAC;0CACE,YAAY,GAAG,CAAC,CAAC;oCAChB,MAAM,aAAa,WAAW,CAAC,KAAK,cAAc,CAAC;oCACnD,qBACE,6LAAC;wCAAiB,WAAU;;0DAC1B,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAA6B,KAAK,UAAU;;;;;;wDAC1D,KAAK,OAAO,kBACX,6LAAC;4DAAI,WAAU;sEAA8B,KAAK,OAAO;;;;;;;;;;;;;;;;;0DAI/D,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAI,WAAU;kEAA6B,KAAK,UAAU;;;;;;oDAC1D,KAAK,UAAU,kBACd,6LAAC;wDAAI,WAAU;kEAA8B,KAAK,UAAU;;;;;;;;;;;;0DAGhE,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAK,WAAU;8DAAyB,KAAK,cAAc;;;;;;;;;;;0DAE9D,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDAAC,WAAW,cAAc,CAAC,KAAK,QAAQ,CAAgC;8DAC3E,KAAK,QAAQ;;;;;;;;;;;0DAGlB,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDAAC,WAAW,YAAY,CAAC,KAAK,cAAc,CAAC;;sEACjD,6LAAC;4DAAW,WAAU;;;;;;wDACrB,KAAK,cAAc;;;;;;;;;;;;0DAGxB,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEAAe,KAAK,SAAS;gEAAC;;;;;;;sEAC7C,6LAAC;4DAAI,WAAU;;gEAAgB;gEAAO,KAAK,QAAQ;;;;;;;;;;;;;;;;;;0DAGvD,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAe,KAAK,SAAS;;;;;;sEAC5C,6LAAC;4DAAI,WAAU;sEAAiB,KAAK,SAAS;;;;;;;;;;;;;;;;;0DAGlD,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC,+IAAA,CAAA,eAAY;;sEACX,6LAAC,+IAAA,CAAA,sBAAmB;4DAAC,OAAO;sEAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;0EAC3B,cAAA,6LAAC,mNAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAG9B,6LAAC,+IAAA,CAAA,sBAAmB;4DAAC,OAAM;;8EACzB,6LAAC,+IAAA,CAAA,mBAAgB;oEAAC,SAAS,IAAM,OAAO;;sFACtC,6LAAC,8MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAGlC,KAAK,cAAc,KAAK,2BACvB;;sFACE,6LAAC,+IAAA,CAAA,mBAAgB;4EAAC,SAAS,IAAM,UAAU,KAAK,EAAE;;8FAChD,6LAAC,uMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGpC,6LAAC,+IAAA,CAAA,mBAAgB;4EAAC,SAAS,IAAM,SAAS,KAAK,EAAE;;8FAC/C,6LAAC,+LAAA,CAAA,IAAC;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;8EAKpC,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IAAM,SAAS,KAAK,EAAE;oEAC/B,WAAU;;sFAEV,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;uCArEpC,KAAK,EAAE;;;;;gCA6EpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd;GArIgB;KAAA", "debugId": null}}, {"offset": {"line": 2052, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 2250, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 2284, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2319, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 2568, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/terminology/CreateTermDialog.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport * as z from 'zod';\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select';\nimport { Loader2 } from 'lucide-react';\n\nconst createTermSchema = z.object({\n  sourceTerm: z.string().min(1, 'Source term is required'),\n  targetTerm: z.string().min(1, 'Target term is required'),\n  targetLanguage: z.string().min(1, 'Target language is required'),\n  category: z.string().min(1, 'Category is required'),\n  context: z.string().optional(),\n  usageNotes: z.string().optional(),\n});\n\ntype CreateTermFormData = z.infer<typeof createTermSchema>;\n\ninterface CreateTermDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  onSubmit: (data: CreateTermFormData) => void;\n}\n\nconst languages = [\n  'Japanese',\n  'Spanish',\n  'French',\n  'German',\n  'Korean',\n  'Chinese (Simplified)',\n  'Chinese (Traditional)',\n  'Portuguese',\n  'Italian',\n  'Russian',\n];\n\nconst categories = [\n  { value: 'technical', label: 'Technical' },\n  { value: 'general', label: 'General' },\n  { value: 'character', label: 'Character' },\n  { value: 'location', label: 'Location' },\n  { value: 'concept', label: 'Concept' },\n  { value: 'custom', label: 'Custom' },\n];\n\nexport function CreateTermDialog({ open, onOpenChange, onSubmit }: CreateTermDialogProps) {\n  const [isLoading, setIsLoading] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    setValue,\n    watch,\n    reset,\n  } = useForm<CreateTermFormData>({\n    resolver: zodResolver(createTermSchema),\n  });\n\n  const targetLanguage = watch('targetLanguage');\n  const category = watch('category');\n\n  const handleFormSubmit = async (data: CreateTermFormData) => {\n    setIsLoading(true);\n    try {\n      await onSubmit(data);\n      reset();\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleClose = () => {\n    reset();\n    onOpenChange(false);\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle>Add New Term</DialogTitle>\n          <DialogDescription>\n            Create a new terminology entry for your translation database.\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit(handleFormSubmit)} className=\"space-y-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"sourceTerm\">Source Term *</Label>\n              <Input\n                id=\"sourceTerm\"\n                placeholder=\"Enter source term\"\n                {...register('sourceTerm')}\n                disabled={isLoading}\n              />\n              {errors.sourceTerm && (\n                <p className=\"text-sm text-red-600\">{errors.sourceTerm.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"targetTerm\">Target Term *</Label>\n              <Input\n                id=\"targetTerm\"\n                placeholder=\"Enter target term\"\n                {...register('targetTerm')}\n                disabled={isLoading}\n              />\n              {errors.targetTerm && (\n                <p className=\"text-sm text-red-600\">{errors.targetTerm.message}</p>\n              )}\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"targetLanguage\">Target Language *</Label>\n              <Select\n                value={targetLanguage}\n                onValueChange={(value) => setValue('targetLanguage', value)}\n                disabled={isLoading}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select language\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {languages.map((language) => (\n                    <SelectItem key={language} value={language}>\n                      {language}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n              {errors.targetLanguage && (\n                <p className=\"text-sm text-red-600\">{errors.targetLanguage.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"category\">Category *</Label>\n              <Select\n                value={category}\n                onValueChange={(value) => setValue('category', value)}\n                disabled={isLoading}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select category\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {categories.map((cat) => (\n                    <SelectItem key={cat.value} value={cat.value}>\n                      {cat.label}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n              {errors.category && (\n                <p className=\"text-sm text-red-600\">{errors.category.message}</p>\n              )}\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"context\">Context</Label>\n            <Textarea\n              id=\"context\"\n              placeholder=\"Provide context for when this term should be used\"\n              {...register('context')}\n              disabled={isLoading}\n              rows={3}\n            />\n            {errors.context && (\n              <p className=\"text-sm text-red-600\">{errors.context.message}</p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"usageNotes\">Usage Notes</Label>\n            <Textarea\n              id=\"usageNotes\"\n              placeholder=\"Add any special usage notes or guidelines\"\n              {...register('usageNotes')}\n              disabled={isLoading}\n              rows={3}\n            />\n            {errors.usageNotes && (\n              <p className=\"text-sm text-red-600\">{errors.usageNotes.message}</p>\n            )}\n          </div>\n\n          <div className=\"flex justify-end space-x-2\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={handleClose}\n              disabled={isLoading}\n            >\n              Cancel\n            </Button>\n            <Button type=\"submit\" disabled={isLoading}>\n              {isLoading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n              Create Term\n            </Button>\n          </div>\n        </form>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AAOA;;;AAxBA;;;;;;;;;;;;AA0BA,MAAM,mBAAmB,CAAA,GAAA,qIAAA,CAAA,SAAQ,AAAD,EAAE;IAChC,YAAY,CAAA,GAAA,qIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC9B,YAAY,CAAA,GAAA,qIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC9B,gBAAgB,CAAA,GAAA,qIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAClC,UAAU,CAAA,GAAA,qIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC5B,SAAS,CAAA,GAAA,qIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IAC5B,YAAY,CAAA,GAAA,qIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;AACjC;AAUA,MAAM,YAAY;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,aAAa;IACjB;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAU,OAAO;IAAS;CACpC;AAEM,SAAS,iBAAiB,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAyB;;IACtF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,QAAQ,EACR,KAAK,EACL,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAsB;QAC9B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,iBAAiB,MAAM;IAC7B,MAAM,WAAW,MAAM;IAEvB,MAAM,mBAAmB,OAAO;QAC9B,aAAa;QACb,IAAI;YACF,MAAM,SAAS;YACf;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB;QACA,aAAa;IACf;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,6LAAC,qIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,6LAAC;oBAAK,UAAU,aAAa;oBAAmB,WAAU;;sCACxD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAa;;;;;;sDAC5B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACX,GAAG,SAAS,aAAa;4CAC1B,UAAU;;;;;;wCAEX,OAAO,UAAU,kBAChB,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;8CAIlE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAa;;;;;;sDAC5B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACX,GAAG,SAAS,aAAa;4CAC1B,UAAU;;;;;;wCAEX,OAAO,UAAU,kBAChB,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;;;;;;;sCAKpE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAiB;;;;;;sDAChC,6LAAC,qIAAA,CAAA,SAAM;4CACL,OAAO;4CACP,eAAe,CAAC,QAAU,SAAS,kBAAkB;4CACrD,UAAU;;8DAEV,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;8DACX,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC,qIAAA,CAAA,aAAU;4DAAgB,OAAO;sEAC/B;2DADc;;;;;;;;;;;;;;;;wCAMtB,OAAO,cAAc,kBACpB,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,cAAc,CAAC,OAAO;;;;;;;;;;;;8CAItE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC,qIAAA,CAAA,SAAM;4CACL,OAAO;4CACP,eAAe,CAAC,QAAU,SAAS,YAAY;4CAC/C,UAAU;;8DAEV,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;8DACX,WAAW,GAAG,CAAC,CAAC,oBACf,6LAAC,qIAAA,CAAA,aAAU;4DAAiB,OAAO,IAAI,KAAK;sEACzC,IAAI,KAAK;2DADK,IAAI,KAAK;;;;;;;;;;;;;;;;wCAM/B,OAAO,QAAQ,kBACd,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;sCAKlE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAU;;;;;;8CACzB,6LAAC,uIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,aAAY;oCACX,GAAG,SAAS,UAAU;oCACvB,UAAU;oCACV,MAAM;;;;;;gCAEP,OAAO,OAAO,kBACb,6LAAC;oCAAE,WAAU;8CAAwB,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;sCAI/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAa;;;;;;8CAC5B,6LAAC,uIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,aAAY;oCACX,GAAG,SAAS,aAAa;oCAC1B,UAAU;oCACV,MAAM;;;;;;gCAEP,OAAO,UAAU,kBAChB,6LAAC;oCAAE,WAAU;8CAAwB,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;sCAIlE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;;wCAC7B,2BAAa,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9E;GArKgB;;QAUV,iKAAA,CAAA,UAAO;;;KAVG", "debugId": null}}, {"offset": {"line": 3055, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 3106, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 3151, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/terminology/ImportTerminologyDialog.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport { Label } from '@/components/ui/label';\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { Progress } from '@/components/ui/progress';\nimport { \n  Upload, \n  FileText, \n  Download, \n  CheckCircle, \n  AlertCircle,\n  Loader2\n} from 'lucide-react';\n\ninterface ImportTerminologyDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  onImport: (data: any) => void;\n}\n\nconst supportedFormats = [\n  { value: 'csv', label: 'CSV', description: 'Comma-separated values' },\n  { value: 'tmx', label: 'TMX', description: 'Translation Memory eXchange' },\n  { value: 'excel', label: 'Excel', description: 'Microsoft Excel (.xlsx)' },\n];\n\nconst fieldMappings = [\n  { key: 'sourceTerm', label: 'Source Term', required: true },\n  { key: 'targetTerm', label: 'Target Term', required: true },\n  { key: 'targetLanguage', label: 'Target Language', required: true },\n  { key: 'category', label: 'Category', required: false },\n  { key: 'context', label: 'Context', required: false },\n  { key: 'usageNotes', label: 'Usage Notes', required: false },\n];\n\nexport function ImportTerminologyDialog({ open, onOpenChange, onImport }: ImportTerminologyDialogProps) {\n  const [step, setStep] = useState(1);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [format, setFormat] = useState('');\n  const [mapping, setMapping] = useState<Record<string, string>>({});\n  const [overwriteExisting, setOverwriteExisting] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [results, setResults] = useState<any>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      setSelectedFile(file);\n      // Auto-detect format based on file extension\n      const extension = file.name.split('.').pop()?.toLowerCase();\n      if (extension === 'csv') setFormat('csv');\n      else if (extension === 'tmx') setFormat('tmx');\n      else if (extension === 'xlsx' || extension === 'xls') setFormat('excel');\n    }\n  };\n\n  const handleNext = () => {\n    if (step < 3) {\n      setStep(step + 1);\n    }\n  };\n\n  const handleBack = () => {\n    if (step > 1) {\n      setStep(step - 1);\n    }\n  };\n\n  const handleImport = async () => {\n    setIsProcessing(true);\n    setProgress(0);\n\n    // Simulate import process\n    const intervals = [20, 40, 60, 80, 100];\n    for (const targetProgress of intervals) {\n      await new Promise(resolve => setTimeout(resolve, 500));\n      setProgress(targetProgress);\n    }\n\n    // Mock results\n    const mockResults = {\n      imported: 45,\n      errors: 2,\n      duplicates: 3,\n      total: 50,\n    };\n\n    setResults(mockResults);\n    setIsProcessing(false);\n    setStep(4);\n  };\n\n  const handleClose = () => {\n    setStep(1);\n    setSelectedFile(null);\n    setFormat('');\n    setMapping({});\n    setOverwriteExisting(false);\n    setIsProcessing(false);\n    setProgress(0);\n    setResults(null);\n    onOpenChange(false);\n  };\n\n  const handleComplete = () => {\n    onImport({\n      file: selectedFile,\n      format,\n      mapping,\n      overwriteExisting,\n      results,\n    });\n    handleClose();\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle>Import Terminology</DialogTitle>\n          <DialogDescription>\n            Import terminology entries from external files\n          </DialogDescription>\n        </DialogHeader>\n\n        {/* Step 1: File Selection */}\n        {step === 1 && (\n          <div className=\"space-y-6\">\n            <div className=\"space-y-4\">\n              <Label>Select File Format</Label>\n              <div className=\"grid grid-cols-1 gap-3\">\n                {supportedFormats.map((fmt) => (\n                  <div\n                    key={fmt.value}\n                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                      format === fmt.value ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                    onClick={() => setFormat(fmt.value)}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <FileText className=\"h-5 w-5 text-gray-400\" />\n                      <div>\n                        <div className=\"font-medium\">{fmt.label}</div>\n                        <div className=\"text-sm text-gray-500\">{fmt.description}</div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"space-y-4\">\n              <Label>Upload File</Label>\n              <div\n                className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400 transition-colors\"\n                onClick={() => fileInputRef.current?.click()}\n              >\n                <Upload className=\"h-8 w-8 text-gray-400 mx-auto mb-2\" />\n                <div className=\"text-sm text-gray-600\">\n                  {selectedFile ? (\n                    <span className=\"font-medium\">{selectedFile.name}</span>\n                  ) : (\n                    <>Click to upload or drag and drop</>\n                  )}\n                </div>\n                <input\n                  ref={fileInputRef}\n                  type=\"file\"\n                  className=\"hidden\"\n                  accept=\".csv,.tmx,.xlsx,.xls\"\n                  onChange={handleFileSelect}\n                />\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-2\">\n              <Button variant=\"outline\" onClick={handleClose}>\n                Cancel\n              </Button>\n              <Button onClick={handleNext} disabled={!selectedFile || !format}>\n                Next\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {/* Step 2: Field Mapping */}\n        {step === 2 && (\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium mb-4\">Map Fields</h3>\n              <p className=\"text-sm text-gray-600 mb-4\">\n                Map the columns in your file to the terminology fields.\n              </p>\n            </div>\n\n            <div className=\"space-y-4\">\n              {fieldMappings.map((field) => (\n                <div key={field.key} className=\"grid grid-cols-2 gap-4 items-center\">\n                  <Label className=\"flex items-center\">\n                    {field.label}\n                    {field.required && <span className=\"text-red-500 ml-1\">*</span>}\n                  </Label>\n                  <Select\n                    value={mapping[field.key] || ''}\n                    onValueChange={(value) => setMapping({ ...mapping, [field.key]: value })}\n                  >\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"Select column\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"column1\">Column 1</SelectItem>\n                      <SelectItem value=\"column2\">Column 2</SelectItem>\n                      <SelectItem value=\"column3\">Column 3</SelectItem>\n                      <SelectItem value=\"column4\">Column 4</SelectItem>\n                      <SelectItem value=\"column5\">Column 5</SelectItem>\n                      <SelectItem value=\"column6\">Column 6</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n              ))}\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"overwrite\"\n                checked={overwriteExisting}\n                onCheckedChange={(checked) => setOverwriteExisting(checked as boolean)}\n              />\n              <Label htmlFor=\"overwrite\" className=\"text-sm\">\n                Overwrite existing terms with the same source term\n              </Label>\n            </div>\n\n            <div className=\"flex justify-between\">\n              <Button variant=\"outline\" onClick={handleBack}>\n                Back\n              </Button>\n              <Button onClick={handleNext}>\n                Next\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {/* Step 3: Confirmation */}\n        {step === 3 && (\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium mb-4\">Confirm Import</h3>\n              <p className=\"text-sm text-gray-600 mb-4\">\n                Review your import settings before proceeding.\n              </p>\n            </div>\n\n            <div className=\"space-y-4 p-4 bg-gray-50 rounded-lg\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm font-medium\">File:</span>\n                <span className=\"text-sm\">{selectedFile?.name}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm font-medium\">Format:</span>\n                <span className=\"text-sm\">{format.toUpperCase()}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm font-medium\">Overwrite existing:</span>\n                <span className=\"text-sm\">{overwriteExisting ? 'Yes' : 'No'}</span>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between\">\n              <Button variant=\"outline\" onClick={handleBack}>\n                Back\n              </Button>\n              <Button onClick={handleImport} disabled={isProcessing}>\n                {isProcessing && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n                Import\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {/* Step 4: Results */}\n        {step === 4 && (\n          <div className=\"space-y-6\">\n            {isProcessing ? (\n              <div className=\"text-center space-y-4\">\n                <Loader2 className=\"h-8 w-8 animate-spin mx-auto\" />\n                <div>\n                  <h3 className=\"text-lg font-medium mb-2\">Importing terminology...</h3>\n                  <Progress value={progress} className=\"w-full\" />\n                  <p className=\"text-sm text-gray-600 mt-2\">{progress}% complete</p>\n                </div>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                <div className=\"text-center\">\n                  <CheckCircle className=\"h-8 w-8 text-green-600 mx-auto mb-2\" />\n                  <h3 className=\"text-lg font-medium\">Import Complete</h3>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div className=\"p-4 bg-green-50 rounded-lg text-center\">\n                    <div className=\"text-2xl font-bold text-green-600\">{results?.imported}</div>\n                    <div className=\"text-sm text-green-700\">Imported</div>\n                  </div>\n                  <div className=\"p-4 bg-yellow-50 rounded-lg text-center\">\n                    <div className=\"text-2xl font-bold text-yellow-600\">{results?.duplicates}</div>\n                    <div className=\"text-sm text-yellow-700\">Duplicates</div>\n                  </div>\n                </div>\n\n                {results?.errors > 0 && (\n                  <div className=\"p-4 bg-red-50 rounded-lg\">\n                    <div className=\"flex items-center\">\n                      <AlertCircle className=\"h-5 w-5 text-red-600 mr-2\" />\n                      <span className=\"text-sm text-red-700\">\n                        {results.errors} entries had errors and were skipped\n                      </span>\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"flex justify-end\">\n                  <Button onClick={handleComplete}>\n                    Done\n                  </Button>\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AAOA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AArBA;;;;;;;;;AAoCA,MAAM,mBAAmB;IACvB;QAAE,OAAO;QAAO,OAAO;QAAO,aAAa;IAAyB;IACpE;QAAE,OAAO;QAAO,OAAO;QAAO,aAAa;IAA8B;IACzE;QAAE,OAAO;QAAS,OAAO;QAAS,aAAa;IAA0B;CAC1E;AAED,MAAM,gBAAgB;IACpB;QAAE,KAAK;QAAc,OAAO;QAAe,UAAU;IAAK;IAC1D;QAAE,KAAK;QAAc,OAAO;QAAe,UAAU;IAAK;IAC1D;QAAE,KAAK;QAAkB,OAAO;QAAmB,UAAU;IAAK;IAClE;QAAE,KAAK;QAAY,OAAO;QAAY,UAAU;IAAM;IACtD;QAAE,KAAK;QAAW,OAAO;QAAW,UAAU;IAAM;IACpD;QAAE,KAAK;QAAc,OAAO;QAAe,UAAU;IAAM;CAC5D;AAEM,SAAS,wBAAwB,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAgC;;IACpG,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAChE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,gBAAgB;YAChB,6CAA6C;YAC7C,MAAM,YAAY,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;YAC9C,IAAI,cAAc,OAAO,UAAU;iBAC9B,IAAI,cAAc,OAAO,UAAU;iBACnC,IAAI,cAAc,UAAU,cAAc,OAAO,UAAU;QAClE;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,OAAO,GAAG;YACZ,QAAQ,OAAO;QACjB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,OAAO,GAAG;YACZ,QAAQ,OAAO;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,gBAAgB;QAChB,YAAY;QAEZ,0BAA0B;QAC1B,MAAM,YAAY;YAAC;YAAI;YAAI;YAAI;YAAI;SAAI;QACvC,KAAK,MAAM,kBAAkB,UAAW;YACtC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,YAAY;QACd;QAEA,eAAe;QACf,MAAM,cAAc;YAClB,UAAU;YACV,QAAQ;YACR,YAAY;YACZ,OAAO;QACT;QAEA,WAAW;QACX,gBAAgB;QAChB,QAAQ;IACV;IAEA,MAAM,cAAc;QAClB,QAAQ;QACR,gBAAgB;QAChB,UAAU;QACV,WAAW,CAAC;QACZ,qBAAqB;QACrB,gBAAgB;QAChB,YAAY;QACZ,WAAW;QACX,aAAa;IACf;IAEA,MAAM,iBAAiB;QACrB,SAAS;YACP,MAAM;YACN;YACA;YACA;YACA;QACF;QACA;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,6LAAC,qIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;gBAMpB,SAAS,mBACR,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,oBACrB,6LAAC;4CAEC,WAAW,CAAC,uDAAuD,EACjE,WAAW,IAAI,KAAK,GAAG,+BAA+B,yCACtD;4CACF,SAAS,IAAM,UAAU,IAAI,KAAK;sDAElC,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAe,IAAI,KAAK;;;;;;0EACvC,6LAAC;gEAAI,WAAU;0EAAyB,IAAI,WAAW;;;;;;;;;;;;;;;;;;2CAVtD,IAAI,KAAK;;;;;;;;;;;;;;;;sCAkBtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,aAAa,OAAO,EAAE;;sDAErC,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAI,WAAU;sDACZ,6BACC,6LAAC;gDAAK,WAAU;0DAAe,aAAa,IAAI;;;;;qEAEhD;0DAAE;;;;;;;sDAGN,6LAAC;4CACC,KAAK;4CACL,MAAK;4CACL,WAAU;4CACV,QAAO;4CACP,UAAU;;;;;;;;;;;;;;;;;;sCAKhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAa;;;;;;8CAGhD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAY,UAAU,CAAC,gBAAgB,CAAC;8CAAQ;;;;;;;;;;;;;;;;;;gBAQtE,SAAS,mBACR,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC;oCAAoB,WAAU;;sDAC7B,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;;gDACd,MAAM,KAAK;gDACX,MAAM,QAAQ,kBAAI,6LAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;sDAEzD,6LAAC,qIAAA,CAAA,SAAM;4CACL,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI;4CAC7B,eAAe,CAAC,QAAU,WAAW;oDAAE,GAAG,OAAO;oDAAE,CAAC,MAAM,GAAG,CAAC,EAAE;gDAAM;;8DAEtE,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;;;;;;;;;;;;;;mCAlBxB,MAAM,GAAG;;;;;;;;;;sCAyBvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,SAAS;oCACT,iBAAiB,CAAC,UAAY,qBAAqB;;;;;;8CAErD,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAAU;;;;;;;;;;;;sCAKjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAY;;;;;;8CAG/C,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;8CAAY;;;;;;;;;;;;;;;;;;gBAQlC,SAAS,mBACR,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,6LAAC;4CAAK,WAAU;sDAAW,cAAc;;;;;;;;;;;;8CAE3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,6LAAC;4CAAK,WAAU;sDAAW,OAAO,WAAW;;;;;;;;;;;;8CAE/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,6LAAC;4CAAK,WAAU;sDAAW,oBAAoB,QAAQ;;;;;;;;;;;;;;;;;;sCAI3D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAY;;;;;;8CAG/C,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAc,UAAU;;wCACtC,8BAAgB,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA+B;;;;;;;;;;;;;;;;;;;gBAQ1E,SAAS,mBACR,6LAAC;oBAAI,WAAU;8BACZ,6BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,OAAO;wCAAU,WAAU;;;;;;kDACrC,6LAAC;wCAAE,WAAU;;4CAA8B;4CAAS;;;;;;;;;;;;;;;;;;6CAIxD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAG,WAAU;kDAAsB;;;;;;;;;;;;0CAGtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAqC,SAAS;;;;;;0DAC7D,6LAAC;gDAAI,WAAU;0DAAyB;;;;;;;;;;;;kDAE1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAsC,SAAS;;;;;;0DAC9D,6LAAC;gDAAI,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;4BAI5C,SAAS,SAAS,mBACjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;;gDACb,QAAQ,MAAM;gDAAC;;;;;;;;;;;;;;;;;;0CAMxB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnD;GA9SgB;KAAA", "debugId": null}}, {"offset": {"line": 4080, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/app/dashboard/terminology/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { DashboardLayout } from '@/components/layout/DashboardLayout';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Badge } from '@/components/ui/badge';\nimport { \n  Plus, \n  Search, \n  Filter, \n  Download, \n  Upload,\n  MoreHorizontal,\n  BookOpen,\n  Check,\n  X,\n  Edit\n} from 'lucide-react';\nimport { TerminologyTable } from '@/components/terminology/TerminologyTable';\nimport { CreateTermDialog } from '@/components/terminology/CreateTermDialog';\nimport { ImportTerminologyDialog } from '@/components/terminology/ImportTerminologyDialog';\n\n// Mock data\nconst mockTerminology = [\n  {\n    id: '1',\n    sourceTerm: 'User Interface',\n    targetTerm: 'ユーザーインターフェース',\n    targetLanguage: 'Japanese',\n    category: 'technical',\n    context: 'Software development context',\n    usageNotes: 'Use this term for all UI-related content',\n    approvalStatus: 'approved',\n    frequency: 45,\n    createdBy: '<PERSON>',\n    reviewedBy: '<PERSON>',\n    lastUsed: '2024-01-15',\n    createdAt: '2024-01-10',\n  },\n  {\n    id: '2',\n    sourceTerm: 'Dashboard',\n    targetTerm: 'ダッシュボード',\n    targetLanguage: 'Japanese',\n    category: 'technical',\n    context: 'Main application interface',\n    usageNotes: 'Preferred over control panel',\n    approvalStatus: 'approved',\n    frequency: 32,\n    createdBy: 'Emma Davis',\n    reviewedBy: 'Sarah Chen',\n    lastUsed: '2024-01-14',\n    createdAt: '2024-01-08',\n  },\n  {\n    id: '3',\n    sourceTerm: 'Settings',\n    targetTerm: '設定',\n    targetLanguage: 'Japanese',\n    category: 'general',\n    context: 'Configuration options',\n    usageNotes: 'Standard translation for settings',\n    approvalStatus: 'pending',\n    frequency: 28,\n    createdBy: 'Alex Kim',\n    reviewedBy: null,\n    lastUsed: '2024-01-13',\n    createdAt: '2024-01-12',\n  },\n  {\n    id: '4',\n    sourceTerm: 'Project',\n    targetTerm: 'プロジェクト',\n    targetLanguage: 'Japanese',\n    category: 'general',\n    context: 'Translation project context',\n    usageNotes: 'Use katakana form',\n    approvalStatus: 'approved',\n    frequency: 67,\n    createdBy: 'Mike Johnson',\n    reviewedBy: 'Sarah Chen',\n    lastUsed: '2024-01-16',\n    createdAt: '2024-01-05',\n  },\n];\n\nconst categories = ['all', 'technical', 'general', 'character', 'location', 'concept'];\nconst languages = ['Japanese', 'Spanish', 'French', 'German', 'Korean'];\nconst statuses = ['all', 'approved', 'pending', 'rejected'];\n\nexport default function TerminologyPage() {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedLanguage, setSelectedLanguage] = useState('all');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showCreateDialog, setShowCreateDialog] = useState(false);\n  const [showImportDialog, setShowImportDialog] = useState(false);\n\n  const filteredTerminology = mockTerminology.filter(term => {\n    const matchesSearch = term.sourceTerm.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         term.targetTerm.toLowerCase().includes(searchQuery.toLowerCase());\n    const matchesCategory = selectedCategory === 'all' || term.category === selectedCategory;\n    const matchesLanguage = selectedLanguage === 'all' || term.targetLanguage === selectedLanguage;\n    const matchesStatus = selectedStatus === 'all' || term.approvalStatus === selectedStatus;\n    \n    return matchesSearch && matchesCategory && matchesLanguage && matchesStatus;\n  });\n\n  const stats = {\n    total: mockTerminology.length,\n    approved: mockTerminology.filter(t => t.approvalStatus === 'approved').length,\n    pending: mockTerminology.filter(t => t.approvalStatus === 'pending').length,\n    rejected: mockTerminology.filter(t => t.approvalStatus === 'rejected').length,\n  };\n\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Terminology Management</h1>\n            <p className=\"text-gray-600\">Manage your translation terminology database</p>\n          </div>\n          <div className=\"flex gap-2 mt-4 sm:mt-0\">\n            <Button variant=\"outline\" onClick={() => setShowImportDialog(true)}>\n              <Upload className=\"mr-2 h-4 w-4\" />\n              Import\n            </Button>\n            <Button variant=\"outline\">\n              <Download className=\"mr-2 h-4 w-4\" />\n              Export\n            </Button>\n            <Button onClick={() => setShowCreateDialog(true)}>\n              <Plus className=\"mr-2 h-4 w-4\" />\n              Add Term\n            </Button>\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <BookOpen className=\"h-6 w-6 text-blue-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Total Terms</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{stats.total}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-green-100 rounded-lg\">\n                  <Check className=\"h-6 w-6 text-green-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Approved</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{stats.approved}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-yellow-100 rounded-lg\">\n                  <Filter className=\"h-6 w-6 text-yellow-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Pending</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{stats.pending}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-red-100 rounded-lg\">\n                  <X className=\"h-6 w-6 text-red-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Rejected</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{stats.rejected}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Filters */}\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"space-y-4\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <Input\n                  placeholder=\"Search terms...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n              \n              <div className=\"flex flex-wrap gap-2\">\n                <div className=\"flex gap-1\">\n                  <span className=\"text-sm font-medium text-gray-700 py-2\">Category:</span>\n                  {categories.map(category => (\n                    <Button\n                      key={category}\n                      variant={selectedCategory === category ? 'default' : 'outline'}\n                      size=\"sm\"\n                      onClick={() => setSelectedCategory(category)}\n                    >\n                      {category.charAt(0).toUpperCase() + category.slice(1)}\n                    </Button>\n                  ))}\n                </div>\n                \n                <div className=\"flex gap-1\">\n                  <span className=\"text-sm font-medium text-gray-700 py-2\">Language:</span>\n                  <Button\n                    variant={selectedLanguage === 'all' ? 'default' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => setSelectedLanguage('all')}\n                  >\n                    All\n                  </Button>\n                  {languages.map(language => (\n                    <Button\n                      key={language}\n                      variant={selectedLanguage === language ? 'default' : 'outline'}\n                      size=\"sm\"\n                      onClick={() => setSelectedLanguage(language)}\n                    >\n                      {language}\n                    </Button>\n                  ))}\n                </div>\n                \n                <div className=\"flex gap-1\">\n                  <span className=\"text-sm font-medium text-gray-700 py-2\">Status:</span>\n                  {statuses.map(status => (\n                    <Button\n                      key={status}\n                      variant={selectedStatus === status ? 'default' : 'outline'}\n                      size=\"sm\"\n                      onClick={() => setSelectedStatus(status)}\n                    >\n                      {status.charAt(0).toUpperCase() + status.slice(1)}\n                    </Button>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Terminology Table */}\n        <TerminologyTable \n          terminology={filteredTerminology}\n          onEdit={(term) => console.log('Edit term:', term)}\n          onDelete={(termId) => console.log('Delete term:', termId)}\n          onApprove={(termId) => console.log('Approve term:', termId)}\n          onReject={(termId) => console.log('Reject term:', termId)}\n        />\n\n        {/* Dialogs */}\n        <CreateTermDialog \n          open={showCreateDialog}\n          onOpenChange={setShowCreateDialog}\n          onSubmit={(data) => {\n            console.log('Create term:', data);\n            setShowCreateDialog(false);\n          }}\n        />\n        \n        <ImportTerminologyDialog\n          open={showImportDialog}\n          onOpenChange={setShowImportDialog}\n          onImport={(data) => {\n            console.log('Import terminology:', data);\n            setShowImportDialog(false);\n          }}\n        />\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;;;AAtBA;;;;;;;;;;AAwBA,YAAY;AACZ,MAAM,kBAAkB;IACtB;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,gBAAgB;QAChB,UAAU;QACV,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,WAAW;QACX,WAAW;QACX,YAAY;QACZ,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,gBAAgB;QAChB,UAAU;QACV,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,WAAW;QACX,WAAW;QACX,YAAY;QACZ,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,gBAAgB;QAChB,UAAU;QACV,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,WAAW;QACX,WAAW;QACX,YAAY;QACZ,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,YAAY;QACZ,YAAY;QACZ,gBAAgB;QAChB,UAAU;QACV,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,WAAW;QACX,WAAW;QACX,YAAY;QACZ,UAAU;QACV,WAAW;IACb;CACD;AAED,MAAM,aAAa;IAAC;IAAO;IAAa;IAAW;IAAa;IAAY;CAAU;AACtF,MAAM,YAAY;IAAC;IAAY;IAAW;IAAU;IAAU;CAAS;AACvE,MAAM,WAAW;IAAC;IAAO;IAAY;IAAW;CAAW;AAE5C,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,sBAAsB,gBAAgB,MAAM,CAAC,CAAA;QACjD,MAAM,gBAAgB,KAAK,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC/D,KAAK,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QACnF,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,QAAQ,KAAK;QACxE,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,cAAc,KAAK;QAC9E,MAAM,gBAAgB,mBAAmB,SAAS,KAAK,cAAc,KAAK;QAE1E,OAAO,iBAAiB,mBAAmB,mBAAmB;IAChE;IAEA,MAAM,QAAQ;QACZ,OAAO,gBAAgB,MAAM;QAC7B,UAAU,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK,YAAY,MAAM;QAC7E,SAAS,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK,WAAW,MAAM;QAC3E,UAAU,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK,YAAY,MAAM;IAC/E;IAEA,qBACE,6LAAC,kJAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,oBAAoB;;sDAC3D,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;;sDACd,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,oBAAoB;;sDACzC,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;8BAOvC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMpE,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMvE,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMtE,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQzE,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAyC;;;;;;gDACxD,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC,qIAAA,CAAA,SAAM;wDAEL,SAAS,qBAAqB,WAAW,YAAY;wDACrD,MAAK;wDACL,SAAS,IAAM,oBAAoB;kEAElC,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;uDAL9C;;;;;;;;;;;sDAUX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAyC;;;;;;8DACzD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS,qBAAqB,QAAQ,YAAY;oDAClD,MAAK;oDACL,SAAS,IAAM,oBAAoB;8DACpC;;;;;;gDAGA,UAAU,GAAG,CAAC,CAAA,yBACb,6LAAC,qIAAA,CAAA,SAAM;wDAEL,SAAS,qBAAqB,WAAW,YAAY;wDACrD,MAAK;wDACL,SAAS,IAAM,oBAAoB;kEAElC;uDALI;;;;;;;;;;;sDAUX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAyC;;;;;;gDACxD,SAAS,GAAG,CAAC,CAAA,uBACZ,6LAAC,qIAAA,CAAA,SAAM;wDAEL,SAAS,mBAAmB,SAAS,YAAY;wDACjD,MAAK;wDACL,SAAS,IAAM,kBAAkB;kEAEhC,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;uDAL1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAenB,6LAAC,wJAAA,CAAA,mBAAgB;oBACf,aAAa;oBACb,QAAQ,CAAC,OAAS,QAAQ,GAAG,CAAC,cAAc;oBAC5C,UAAU,CAAC,SAAW,QAAQ,GAAG,CAAC,gBAAgB;oBAClD,WAAW,CAAC,SAAW,QAAQ,GAAG,CAAC,iBAAiB;oBACpD,UAAU,CAAC,SAAW,QAAQ,GAAG,CAAC,gBAAgB;;;;;;8BAIpD,6LAAC,wJAAA,CAAA,mBAAgB;oBACf,MAAM;oBACN,cAAc;oBACd,UAAU,CAAC;wBACT,QAAQ,GAAG,CAAC,gBAAgB;wBAC5B,oBAAoB;oBACtB;;;;;;8BAGF,6LAAC,+JAAA,CAAA,0BAAuB;oBACtB,MAAM;oBACN,cAAc;oBACd,UAAU,CAAC;wBACT,QAAQ,GAAG,CAAC,uBAAuB;wBACnC,oBAAoB;oBACtB;;;;;;;;;;;;;;;;;AAKV;GA/MwB;KAAA", "debugId": null}}]}