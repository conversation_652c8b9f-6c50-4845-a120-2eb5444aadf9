(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/store/api/organizationsApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "organizationsApi": (()=>organizationsApi),
    "useCreateOrganizationMutation": (()=>useCreateOrganizationMutation),
    "useGetOrganizationQuery": (()=>useGetOrganizationQuery),
    "useGetOrganizationsQuery": (()=>useGetOrganizationsQuery),
    "useInviteUserMutation": (()=>useInviteUserMutation),
    "useRemoveMemberMutation": (()=>useRemoveMemberMutation),
    "useUpdateMemberRoleMutation": (()=>useUpdateMemberRoleMutation),
    "useUpdateOrganizationMutation": (()=>useUpdateOrganizationMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$react$2f$rtk$2d$query$2d$react$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs [app-client] (ecmascript)");
;
const organizationsApi = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$react$2f$rtk$2d$query$2d$react$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createApi"])({
    reducerPath: 'organizationsApi',
    baseQuery: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchBaseQuery"])({
        baseUrl: '/api/organizations',
        prepareHeaders: (headers, { getState })=>{
            // Add authentication headers if needed
            headers.set('Content-Type', 'application/json');
            return headers;
        }
    }),
    tagTypes: [
        'Organization',
        'Member',
        'Subscription'
    ],
    endpoints: (builder)=>({
            getOrganizations: builder.query({
                query: ()=>'',
                providesTags: [
                    'Organization'
                ]
            }),
            createOrganization: builder.mutation({
                query: (body)=>({
                        url: '',
                        method: 'POST',
                        body
                    }),
                invalidatesTags: [
                    'Organization'
                ]
            }),
            getOrganization: builder.query({
                query: (orgId)=>`/${orgId}`,
                providesTags: (result, error, orgId)=>[
                        {
                            type: 'Organization',
                            id: orgId
                        },
                        'Member',
                        'Subscription'
                    ]
            }),
            updateOrganization: builder.mutation({
                query: ({ orgId, data })=>({
                        url: `/${orgId}`,
                        method: 'PUT',
                        body: data
                    }),
                invalidatesTags: (result, error, { orgId })=>[
                        {
                            type: 'Organization',
                            id: orgId
                        }
                    ]
            }),
            inviteUser: builder.mutation({
                query: ({ orgId, data })=>({
                        url: `/${orgId}/invite`,
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: [
                    'Member'
                ]
            }),
            removeMember: builder.mutation({
                query: ({ orgId, memberId })=>({
                        url: `/${orgId}/members/${memberId}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: [
                    'Member'
                ]
            }),
            updateMemberRole: builder.mutation({
                query: ({ orgId, memberId, role })=>({
                        url: `/${orgId}/members/${memberId}`,
                        method: 'PUT',
                        body: {
                            role
                        }
                    }),
                invalidatesTags: [
                    'Member'
                ]
            })
        })
});
const { useGetOrganizationsQuery, useCreateOrganizationMutation, useGetOrganizationQuery, useUpdateOrganizationMutation, useInviteUserMutation, useRemoveMemberMutation, useUpdateMemberRoleMutation } = organizationsApi;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/projectsApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "projectsApi": (()=>projectsApi),
    "useAssignReviewerMutation": (()=>useAssignReviewerMutation),
    "useAssignTranslatorMutation": (()=>useAssignTranslatorMutation),
    "useCreateProjectMutation": (()=>useCreateProjectMutation),
    "useDeleteProjectMutation": (()=>useDeleteProjectMutation),
    "useGetProjectQuery": (()=>useGetProjectQuery),
    "useGetProjectStatsQuery": (()=>useGetProjectStatsQuery),
    "useGetProjectsQuery": (()=>useGetProjectsQuery),
    "useGetSegmentsQuery": (()=>useGetSegmentsQuery),
    "useUpdateProjectMutation": (()=>useUpdateProjectMutation),
    "useUploadDocumentsMutation": (()=>useUploadDocumentsMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$react$2f$rtk$2d$query$2d$react$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs [app-client] (ecmascript)");
;
const projectsApi = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$react$2f$rtk$2d$query$2d$react$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createApi"])({
    reducerPath: 'projectsApi',
    baseQuery: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchBaseQuery"])({
        baseUrl: '/api/projects',
        prepareHeaders: (headers)=>{
            headers.set('Content-Type', 'application/json');
            return headers;
        }
    }),
    tagTypes: [
        'Project',
        'Segment',
        'Document',
        'Terminology'
    ],
    endpoints: (builder)=>({
            getProjects: builder.query({
                query: (params)=>({
                        url: '',
                        params
                    }),
                providesTags: [
                    'Project'
                ]
            }),
            createProject: builder.mutation({
                query: (body)=>({
                        url: '',
                        method: 'POST',
                        body
                    }),
                invalidatesTags: [
                    'Project'
                ]
            }),
            getProject: builder.query({
                query: (projectId)=>`/${projectId}`,
                providesTags: (result, error, projectId)=>[
                        {
                            type: 'Project',
                            id: projectId
                        },
                        'Document',
                        'Terminology'
                    ]
            }),
            updateProject: builder.mutation({
                query: ({ projectId, data })=>({
                        url: `/${projectId}`,
                        method: 'PUT',
                        body: data
                    }),
                invalidatesTags: (result, error, { projectId })=>[
                        {
                            type: 'Project',
                            id: projectId
                        }
                    ]
            }),
            deleteProject: builder.mutation({
                query: (projectId)=>({
                        url: `/${projectId}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: [
                    'Project'
                ]
            }),
            uploadDocuments: builder.mutation({
                query: ({ projectId, data })=>({
                        url: `/${projectId}/documents`,
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: [
                    'Document',
                    'Segment'
                ]
            }),
            getSegments: builder.query({
                query: ({ projectId, ...params })=>({
                        url: `/${projectId}/segments`,
                        params
                    }),
                providesTags: [
                    'Segment'
                ]
            }),
            assignTranslator: builder.mutation({
                query: ({ projectId, segmentIds, translatorId })=>({
                        url: `/${projectId}/assign-translator`,
                        method: 'POST',
                        body: {
                            segmentIds,
                            translatorId
                        }
                    }),
                invalidatesTags: [
                    'Segment'
                ]
            }),
            assignReviewer: builder.mutation({
                query: ({ projectId, segmentIds, reviewerId })=>({
                        url: `/${projectId}/assign-reviewer`,
                        method: 'POST',
                        body: {
                            segmentIds,
                            reviewerId
                        }
                    }),
                invalidatesTags: [
                    'Segment'
                ]
            }),
            getProjectStats: builder.query({
                query: (projectId)=>`/${projectId}/stats`,
                providesTags: (result, error, projectId)=>[
                        {
                            type: 'Project',
                            id: `${projectId}-stats`
                        }
                    ]
            })
        })
});
const { useGetProjectsQuery, useCreateProjectMutation, useGetProjectQuery, useUpdateProjectMutation, useDeleteProjectMutation, useUploadDocumentsMutation, useGetSegmentsQuery, useAssignTranslatorMutation, useAssignReviewerMutation, useGetProjectStatsQuery } = projectsApi;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/translationApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "translationApi": (()=>translationApi),
    "useBulkUpdateSegmentsMutation": (()=>useBulkUpdateSegmentsMutation),
    "useCreateCommentMutation": (()=>useCreateCommentMutation),
    "useCreateReviewMutation": (()=>useCreateReviewMutation),
    "useDeleteCommentMutation": (()=>useDeleteCommentMutation),
    "useGetSegmentHistoryQuery": (()=>useGetSegmentHistoryQuery),
    "useGetSegmentQuery": (()=>useGetSegmentQuery),
    "useGetTranslationMemoryMatchesQuery": (()=>useGetTranslationMemoryMatchesQuery),
    "useUpdateCommentMutation": (()=>useUpdateCommentMutation),
    "useUpdateSegmentMutation": (()=>useUpdateSegmentMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$react$2f$rtk$2d$query$2d$react$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs [app-client] (ecmascript)");
;
const translationApi = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$react$2f$rtk$2d$query$2d$react$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createApi"])({
    reducerPath: 'translationApi',
    baseQuery: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchBaseQuery"])({
        baseUrl: '/api/segments',
        prepareHeaders: (headers)=>{
            headers.set('Content-Type', 'application/json');
            return headers;
        }
    }),
    tagTypes: [
        'Segment',
        'Comment',
        'Review'
    ],
    endpoints: (builder)=>({
            getSegment: builder.query({
                query: (segmentId)=>`/${segmentId}`,
                providesTags: (result, error, segmentId)=>[
                        {
                            type: 'Segment',
                            id: segmentId
                        },
                        'Comment',
                        'Review'
                    ]
            }),
            updateSegment: builder.mutation({
                query: ({ segmentId, data })=>({
                        url: `/${segmentId}`,
                        method: 'PUT',
                        body: data
                    }),
                invalidatesTags: (result, error, { segmentId })=>[
                        {
                            type: 'Segment',
                            id: segmentId
                        }
                    ],
                // Optimistic update for better UX
                async onQueryStarted ({ segmentId, data }, { dispatch, queryFulfilled }) {
                    const patchResult = dispatch(translationApi.util.updateQueryData('getSegment', segmentId, (draft)=>{
                        Object.assign(draft.segment, data);
                    }));
                    try {
                        await queryFulfilled;
                    } catch  {
                        patchResult.undo();
                    }
                }
            }),
            createComment: builder.mutation({
                query: ({ segmentId, data })=>({
                        url: `/${segmentId}/comments`,
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: [
                    'Comment'
                ]
            }),
            updateComment: builder.mutation({
                query: ({ commentId, data })=>({
                        url: `/comments/${commentId}`,
                        method: 'PUT',
                        body: data
                    }),
                invalidatesTags: [
                    'Comment'
                ]
            }),
            deleteComment: builder.mutation({
                query: (commentId)=>({
                        url: `/comments/${commentId}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: [
                    'Comment'
                ]
            }),
            createReview: builder.mutation({
                query: ({ segmentId, data })=>({
                        url: `/${segmentId}/reviews`,
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: [
                    'Review'
                ]
            }),
            getSegmentHistory: builder.query({
                query: (segmentId)=>`/${segmentId}/history`
            }),
            bulkUpdateSegments: builder.mutation({
                query: ({ segmentIds, data })=>({
                        url: '/bulk-update',
                        method: 'PUT',
                        body: {
                            segmentIds,
                            data
                        }
                    }),
                invalidatesTags: [
                    'Segment'
                ]
            }),
            getTranslationMemoryMatches: builder.query({
                query: ({ projectId, sourceText, targetLanguage })=>({
                        url: '/translation-memory',
                        params: {
                            projectId,
                            sourceText,
                            targetLanguage
                        }
                    })
            })
        })
});
const { useGetSegmentQuery, useUpdateSegmentMutation, useCreateCommentMutation, useUpdateCommentMutation, useDeleteCommentMutation, useCreateReviewMutation, useGetSegmentHistoryQuery, useBulkUpdateSegmentsMutation, useGetTranslationMemoryMatchesQuery } = translationApi;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/api/terminologyApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "terminologyApi": (()=>terminologyApi),
    "useApproveTerminologyEntryMutation": (()=>useApproveTerminologyEntryMutation),
    "useBulkApproveTerminologyMutation": (()=>useBulkApproveTerminologyMutation),
    "useBulkDeleteTerminologyMutation": (()=>useBulkDeleteTerminologyMutation),
    "useCreateTerminologyEntryMutation": (()=>useCreateTerminologyEntryMutation),
    "useDeleteTerminologyEntryMutation": (()=>useDeleteTerminologyEntryMutation),
    "useExportTerminologyMutation": (()=>useExportTerminologyMutation),
    "useGetTerminologyQuery": (()=>useGetTerminologyQuery),
    "useGetTerminologyStatsQuery": (()=>useGetTerminologyStatsQuery),
    "useImportTerminologyMutation": (()=>useImportTerminologyMutation),
    "useSearchTerminologyQuery": (()=>useSearchTerminologyQuery),
    "useUpdateTerminologyEntryMutation": (()=>useUpdateTerminologyEntryMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$react$2f$rtk$2d$query$2d$react$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs [app-client] (ecmascript)");
;
const terminologyApi = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$react$2f$rtk$2d$query$2d$react$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createApi"])({
    reducerPath: 'terminologyApi',
    baseQuery: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchBaseQuery"])({
        baseUrl: '/api/terminology',
        prepareHeaders: (headers)=>{
            headers.set('Content-Type', 'application/json');
            return headers;
        }
    }),
    tagTypes: [
        'Terminology',
        'TerminologyStats'
    ],
    endpoints: (builder)=>({
            getTerminology: builder.query({
                query: ({ projectId, ...params })=>({
                        url: `/projects/${projectId}`,
                        params
                    }),
                providesTags: [
                    'Terminology'
                ]
            }),
            createTerminologyEntry: builder.mutation({
                query: ({ projectId, data })=>({
                        url: `/projects/${projectId}`,
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: [
                    'Terminology',
                    'TerminologyStats'
                ]
            }),
            updateTerminologyEntry: builder.mutation({
                query: ({ entryId, data })=>({
                        url: `/${entryId}`,
                        method: 'PUT',
                        body: data
                    }),
                invalidatesTags: [
                    'Terminology'
                ]
            }),
            deleteTerminologyEntry: builder.mutation({
                query: (entryId)=>({
                        url: `/${entryId}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: [
                    'Terminology',
                    'TerminologyStats'
                ]
            }),
            approveTerminologyEntry: builder.mutation({
                query: ({ entryId, approved })=>({
                        url: `/${entryId}/approve`,
                        method: 'PUT',
                        body: {
                            approved
                        }
                    }),
                invalidatesTags: [
                    'Terminology'
                ]
            }),
            importTerminology: builder.mutation({
                query: ({ projectId, data })=>({
                        url: `/projects/${projectId}/import`,
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: [
                    'Terminology',
                    'TerminologyStats'
                ]
            }),
            exportTerminology: builder.mutation({
                query: ({ projectId, format, language })=>({
                        url: `/projects/${projectId}/export`,
                        method: 'POST',
                        body: {
                            format,
                            language
                        }
                    })
            }),
            searchTerminology: builder.query({
                query: ({ projectId, text, targetLanguage })=>({
                        url: `/projects/${projectId}/search`,
                        params: {
                            text,
                            targetLanguage
                        }
                    })
            }),
            getTerminologyStats: builder.query({
                query: (projectId)=>`/projects/${projectId}/stats`,
                providesTags: [
                    'TerminologyStats'
                ]
            }),
            bulkApproveTerminology: builder.mutation({
                query: ({ entryIds, approved })=>({
                        url: '/bulk-approve',
                        method: 'PUT',
                        body: {
                            entryIds,
                            approved
                        }
                    }),
                invalidatesTags: [
                    'Terminology'
                ]
            }),
            bulkDeleteTerminology: builder.mutation({
                query: (entryIds)=>({
                        url: '/bulk-delete',
                        method: 'DELETE',
                        body: {
                            entryIds
                        }
                    }),
                invalidatesTags: [
                    'Terminology',
                    'TerminologyStats'
                ]
            })
        })
});
const { useGetTerminologyQuery, useCreateTerminologyEntryMutation, useUpdateTerminologyEntryMutation, useDeleteTerminologyEntryMutation, useApproveTerminologyEntryMutation, useImportTerminologyMutation, useExportTerminologyMutation, useSearchTerminologyQuery, useGetTerminologyStatsQuery, useBulkApproveTerminologyMutation, useBulkDeleteTerminologyMutation } = terminologyApi;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/slices/authSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "logout": (()=>logout),
    "setCurrentOrganization": (()=>setCurrentOrganization),
    "setLoading": (()=>setLoading),
    "setUser": (()=>setUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    user: null,
    isLoading: true,
    isAuthenticated: false,
    currentOrganizationId: null
};
const authSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'auth',
    initialState,
    reducers: {
        setUser: (state, action)=>{
            state.user = action.payload;
            state.isAuthenticated = !!action.payload;
            state.isLoading = false;
        },
        setLoading: (state, action)=>{
            state.isLoading = action.payload;
        },
        setCurrentOrganization: (state, action)=>{
            state.currentOrganizationId = action.payload;
        },
        logout: (state)=>{
            state.user = null;
            state.isAuthenticated = false;
            state.currentOrganizationId = null;
            state.isLoading = false;
        }
    }
});
const { setUser, setLoading, setCurrentOrganization, logout } = authSlice.actions;
const __TURBOPACK__default__export__ = authSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/slices/uiSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addNotification": (()=>addNotification),
    "clearNotifications": (()=>clearNotifications),
    "default": (()=>__TURBOPACK__default__export__),
    "markNotificationRead": (()=>markNotificationRead),
    "removeNotification": (()=>removeNotification),
    "setActiveModal": (()=>setActiveModal),
    "setLoading": (()=>setLoading),
    "setSidebarOpen": (()=>setSidebarOpen),
    "setTheme": (()=>setTheme),
    "toggleSidebar": (()=>toggleSidebar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    sidebarOpen: true,
    theme: 'system',
    notifications: [],
    activeModal: null,
    loading: {}
};
const uiSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'ui',
    initialState,
    reducers: {
        toggleSidebar: (state)=>{
            state.sidebarOpen = !state.sidebarOpen;
        },
        setSidebarOpen: (state, action)=>{
            state.sidebarOpen = action.payload;
        },
        setTheme: (state, action)=>{
            state.theme = action.payload;
        },
        addNotification: (state, action)=>{
            const notification = {
                ...action.payload,
                id: Date.now().toString(),
                timestamp: new Date().toISOString(),
                read: false
            };
            state.notifications.unshift(notification);
        },
        markNotificationRead: (state, action)=>{
            const notification = state.notifications.find((n)=>n.id === action.payload);
            if (notification) {
                notification.read = true;
            }
        },
        removeNotification: (state, action)=>{
            state.notifications = state.notifications.filter((n)=>n.id !== action.payload);
        },
        clearNotifications: (state)=>{
            state.notifications = [];
        },
        setActiveModal: (state, action)=>{
            state.activeModal = action.payload;
        },
        setLoading: (state, action)=>{
            state.loading[action.payload.key] = action.payload.loading;
        }
    }
});
const { toggleSidebar, setSidebarOpen, setTheme, addNotification, markNotificationRead, removeNotification, clearNotifications, setActiveModal, setLoading } = uiSlice.actions;
const __TURBOPACK__default__export__ = uiSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "store": (()=>store)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$organizationsApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/organizationsApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$projectsApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/projectsApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$translationApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/translationApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$terminologyApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/api/terminologyApi.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/authSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$uiSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/uiSlice.ts [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
const store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["configureStore"])({
    reducer: {
        auth: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        ui: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$uiSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$organizationsApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["organizationsApi"].reducerPath]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$organizationsApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["organizationsApi"].reducer,
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$projectsApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["projectsApi"].reducerPath]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$projectsApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["projectsApi"].reducer,
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$translationApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["translationApi"].reducerPath]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$translationApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["translationApi"].reducer,
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$terminologyApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["terminologyApi"].reducerPath]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$terminologyApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["terminologyApi"].reducer
    },
    middleware: (getDefaultMiddleware)=>getDefaultMiddleware({
            serializableCheck: {
                ignoredActions: [
                    'persist/PERSIST',
                    'persist/REHYDRATE'
                ]
            }
        }).concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$organizationsApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["organizationsApi"].middleware).concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$projectsApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["projectsApi"].middleware).concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$translationApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["translationApi"].middleware).concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$api$2f$terminologyApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["terminologyApi"].middleware)
});
// Setup listeners for automatic refetching
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setupListeners"])(store.dispatch);
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/providers/ReduxProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReduxProvider": (()=>ReduxProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/index.ts [app-client] (ecmascript)");
'use client';
;
;
;
function ReduxProvider({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Provider"], {
        store: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["store"],
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/providers/ReduxProvider.tsx",
        lineNumber: 11,
        columnNumber: 10
    }, this);
}
_c = ReduxProvider;
var _c;
__turbopack_context__.k.register(_c, "ReduxProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/hooks.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAppDispatch": (()=>useAppDispatch),
    "useAppSelector": (()=>useAppSelector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
const useAppDispatch = ()=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"])();
};
_s(useAppDispatch, "jI3HA1r1Cumjdbu14H7G+TUj798=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"]
    ];
});
const useAppSelector = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/providers/AuthProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/authSlice.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
function AuthStateManager({ children }) {
    _s();
    const { data: session, status } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSession"])();
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppDispatch"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthStateManager.useEffect": ()=>{
            if (status === 'loading') {
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setLoading"])(true));
            } else {
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setLoading"])(false));
                if (session?.user) {
                    dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setUser"])({
                        id: session.user.id || '',
                        email: session.user.email || '',
                        name: session.user.name || null,
                        avatar_url: session.user.image || null,
                        email_verified: true,
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    }));
                } else {
                    dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setUser"])(null));
                }
            }
        }
    }["AuthStateManager.useEffect"], [
        session,
        status,
        dispatch
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(AuthStateManager, "7wFhtp757xEk6S07TC0Pub2Mk9k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSession"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppDispatch"]
    ];
});
_c = AuthStateManager;
function AuthProvider({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SessionProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthStateManager, {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/providers/AuthProvider.tsx",
            lineNumber: 45,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/providers/AuthProvider.tsx",
        lineNumber: 44,
        columnNumber: 5
    }, this);
}
_c1 = AuthProvider;
var _c, _c1;
__turbopack_context__.k.register(_c, "AuthStateManager");
__turbopack_context__.k.register(_c1, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_68a51b91._.js.map