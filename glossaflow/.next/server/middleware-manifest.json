{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "tAmvl4mg1oDWe5Dsprh8Ih7p7p0YuTcmWBNmQzoBO34=", "__NEXT_PREVIEW_MODE_ID": "88060902beaece8aa0eb54f10da0cca6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9fea680b0f75d63741188c626270bead561068fd690e6dca348b38676d38ab04", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "53bbee8ebc6e65c08783392ced1de49bc35b15fd69f028055980c8c3e6f61e2b"}}}, "sortedMiddleware": ["/"], "functions": {}}