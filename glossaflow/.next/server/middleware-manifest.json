{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "tAmvl4mg1oDWe5Dsprh8Ih7p7p0YuTcmWBNmQzoBO34=", "__NEXT_PREVIEW_MODE_ID": "76816abc4a719a53798a2e7f1aabc7c6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8f88f7431443fa6c5608d3d4078bd8367ba3318de7d6063ed32b4b4f91329c27", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "292ad0afc4c88bc50b7085693fd4529fc4bdb9eaadd1318cd92f2d0b59fa3f79"}}}, "sortedMiddleware": ["/"], "functions": {}}