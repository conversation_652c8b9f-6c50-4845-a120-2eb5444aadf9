module.exports = {

"[project]/.next-internal/server/app/api/dashboard/activity/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$google$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/google.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$github$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/github.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
;
;
;
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(("TURBOPACK compile-time value", "https://awhtjodmtstnsobmagfc.supabase.co"), process.env.SUPABASE_SERVICE_ROLE_KEY);
const authOptions = {
    // Remove adapter temporarily to test without database integration
    // adapter: SupabaseAdapter({
    //   url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    //   secret: process.env.SUPABASE_SERVICE_ROLE_KEY!,
    // }),
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$google$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET
        }),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$github$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            clientId: process.env.GITHUB_CLIENT_ID,
            clientSecret: process.env.GITHUB_CLIENT_SECRET
        }),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'credentials',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                password: {
                    label: 'Password',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.password) return null;
                try {
                    const { data, error } = await supabase.auth.signInWithPassword({
                        email: credentials.email,
                        password: credentials.password
                    });
                    if (error || !data.user) return null;
                    return {
                        id: data.user.id,
                        email: data.user.email,
                        name: data.user.user_metadata?.name || data.user.email,
                        image: data.user.user_metadata?.avatar_url
                    };
                } catch (error) {
                    console.error('Auth error:', error);
                    return null;
                }
            }
        })
    ],
    callbacks: {
        async signIn ({ user, account, profile }) {
            if (account?.provider === 'google' && profile?.email) {
                try {
                    // Create Supabase client
                    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(("TURBOPACK compile-time value", "https://awhtjodmtstnsobmagfc.supabase.co"), process.env.SUPABASE_SERVICE_ROLE_KEY);
                    // Check if user exists
                    const { data: existingUser } = await supabase.from('users').select('*').eq('email', profile.email).single();
                    if (!existingUser) {
                        // Create new user
                        const { error } = await supabase.from('users').insert({
                            email: profile.email,
                            name: profile.name || user.name,
                            avatar_url: profile.picture || user.image,
                            email_verified: new Date().toISOString()
                        });
                        if (error) {
                            console.error('Error creating user:', error);
                            return false;
                        }
                    }
                    return true;
                } catch (error) {
                    console.error('Sign in error:', error);
                    return false;
                }
            }
            return true;
        },
        async session ({ session, token }) {
            if (token && session.user) {
                // Get user data from Supabase
                try {
                    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(("TURBOPACK compile-time value", "https://awhtjodmtstnsobmagfc.supabase.co"), process.env.SUPABASE_SERVICE_ROLE_KEY);
                    const { data: userData } = await supabase.from('users').select('*').eq('email', session.user.email).single();
                    if (userData) {
                        session.user.id = userData.id;
                        session.user.name = userData.name;
                        session.user.image = userData.avatar_url;
                    }
                } catch (error) {
                    console.error('Session error:', error);
                }
            }
            return session;
        },
        async jwt ({ token, user, account, profile }) {
            if (user) {
                token.sub = user.id;
            }
            return token;
        },
        async signIn ({ user, account, profile }) {
            // Allow sign in
            return true;
        },
        async redirect ({ url, baseUrl }) {
            // Allows relative callback URLs
            if (url.startsWith('/')) return `${baseUrl}${url}`;
            else if (new URL(url).origin === baseUrl) return url;
            return baseUrl;
        }
    },
    pages: {
        signIn: '/auth/signin',
        signUp: '/auth/signup',
        error: '/auth/error',
        verifyRequest: '/auth/verify-request'
    },
    session: {
        strategy: 'jwt'
    },
    secret: process.env.NEXTAUTH_SECRET
};
}}),
"[project]/src/app/api/dashboard/activity/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$next$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/next/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
;
;
;
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(("TURBOPACK compile-time value", "https://awhtjodmtstnsobmagfc.supabase.co"), process.env.SUPABASE_SERVICE_ROLE_KEY);
async function GET(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$next$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session?.user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized',
                success: false
            }, {
                status: 401
            });
        }
        const { searchParams } = new URL(request.url);
        const limit = parseInt(searchParams.get('limit') || '10');
        // Get organization ID from session or use a default for now
        const organizationId = session.user.organizationId || null;
        // Get user information for activity attribution
        const { data: users } = await supabase.auth.admin.listUsers();
        const userMap = new Map();
        users?.users?.forEach((user)=>{
            userMap.set(user.id, {
                id: user.id,
                name: user.user_metadata?.name || user.user_metadata?.full_name || 'Unknown User',
                email: user.email,
                avatar: user.user_metadata?.avatar_url || user.user_metadata?.picture
            });
        });
        // Fetch recent project activities
        const { data: recentProjects, error: projectsError } = await supabase.from('projects').select('id, name, status, created_at, updated_at, created_by').eq('organization_id', organizationId).order('updated_at', {
            ascending: false
        }).limit(limit);
        if (projectsError) {
            console.error('Recent projects fetch error:', projectsError);
        }
        // Fetch recent terminology activities
        const { data: recentTerms, error: termsError } = await supabase.from('terminology').select('id, term, status, created_at, updated_at, created_by').eq('organization_id', organizationId).order('updated_at', {
            ascending: false
        }).limit(limit);
        if (termsError) {
            console.error('Recent terms fetch error:', termsError);
        }
        // Fetch recent team activities
        const { data: recentTeamMembers, error: teamError } = await supabase.from('team_members').select('id, role_name, status, created_at, updated_at, created_by, user_id').eq('organization_id', organizationId).order('updated_at', {
            ascending: false
        }).limit(limit);
        if (teamError) {
            console.error('Recent team members fetch error:', teamError);
        }
        // Fetch recent invitations
        const { data: recentInvitations, error: invitationsError } = await supabase.from('team_invitations').select('id, email, role_name, status, created_at, updated_at, invited_by').eq('organization_id', organizationId).order('created_at', {
            ascending: false
        }).limit(limit);
        if (invitationsError) {
            console.error('Recent invitations fetch error:', invitationsError);
        }
        // Create activity feed items
        const activities = [];
        // Add project activities
        recentProjects?.forEach((project)=>{
            const user = userMap.get(project.created_by);
            const isNew = new Date(project.created_at).getTime() === new Date(project.updated_at).getTime();
            activities.push({
                id: `project-${project.id}`,
                type: 'project',
                action: isNew ? 'created' : 'updated',
                title: project.name,
                description: isNew ? `${user?.name || 'Someone'} created project "${project.name}"` : `Project "${project.name}" status changed to ${project.status}`,
                user: user || {
                    name: 'Unknown User',
                    email: '',
                    avatar: null
                },
                timestamp: project.updated_at,
                metadata: {
                    projectId: project.id,
                    status: project.status
                }
            });
        });
        // Add terminology activities
        recentTerms?.forEach((term)=>{
            const user = userMap.get(term.created_by);
            const isNew = new Date(term.created_at).getTime() === new Date(term.updated_at).getTime();
            activities.push({
                id: `term-${term.id}`,
                type: 'terminology',
                action: isNew ? 'created' : 'updated',
                title: term.term,
                description: isNew ? `${user?.name || 'Someone'} added term "${term.term}"` : `Term "${term.term}" status changed to ${term.status}`,
                user: user || {
                    name: 'Unknown User',
                    email: '',
                    avatar: null
                },
                timestamp: term.updated_at,
                metadata: {
                    termId: term.id,
                    status: term.status
                }
            });
        });
        // Add team member activities
        recentTeamMembers?.forEach((member)=>{
            const user = userMap.get(member.created_by);
            const memberUser = userMap.get(member.user_id);
            const isNew = new Date(member.created_at).getTime() === new Date(member.updated_at).getTime();
            activities.push({
                id: `team-${member.id}`,
                type: 'team',
                action: isNew ? 'joined' : 'updated',
                title: `${memberUser?.name || 'Team Member'}`,
                description: isNew ? `${memberUser?.name || 'Someone'} joined as ${member.role_name}` : `${memberUser?.name || 'Team member'} status changed to ${member.status}`,
                user: memberUser || {
                    name: 'Unknown User',
                    email: '',
                    avatar: null
                },
                timestamp: member.updated_at,
                metadata: {
                    memberId: member.id,
                    role: member.role_name,
                    status: member.status
                }
            });
        });
        // Add invitation activities
        recentInvitations?.forEach((invitation)=>{
            const user = userMap.get(invitation.invited_by);
            activities.push({
                id: `invitation-${invitation.id}`,
                type: 'invitation',
                action: invitation.status === 'pending' ? 'invited' : invitation.status,
                title: invitation.email,
                description: invitation.status === 'pending' ? `${user?.name || 'Someone'} invited ${invitation.email} as ${invitation.role_name}` : `Invitation to ${invitation.email} was ${invitation.status}`,
                user: user || {
                    name: 'Unknown User',
                    email: '',
                    avatar: null
                },
                timestamp: invitation.updated_at || invitation.created_at,
                metadata: {
                    invitationId: invitation.id,
                    email: invitation.email,
                    role: invitation.role_name,
                    status: invitation.status
                }
            });
        });
        // Sort activities by timestamp (most recent first)
        activities.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
        // Limit to requested number of activities
        const limitedActivities = activities.slice(0, limit);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                activities: limitedActivities,
                total: activities.length
            }
        });
    } catch (error) {
        console.error('Dashboard activity API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error',
            success: false
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__4eabcaa9._.js.map