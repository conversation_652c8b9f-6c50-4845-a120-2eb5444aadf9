{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/api/base.ts"], "sourcesContent": ["import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';\nimport { getSession } from 'next-auth/react';\n\nconst baseQuery = fetchBaseQuery({\n  baseUrl: '/api',\n  prepareHeaders: async (headers) => {\n    const session = await getSession();\n    if (session?.accessToken) {\n      headers.set('authorization', `Bearer ${session.accessToken}`);\n    }\n    return headers;\n  },\n});\n\nexport const baseApi = createApi({\n  reducerPath: 'api',\n  baseQuery,\n  tagTypes: [\n    'User',\n    'Project',\n    'Terminology',\n    'Translation',\n    'Comment',\n    'File',\n    'Team',\n    'Organization',\n  ],\n  endpoints: () => ({}),\n});\n\nexport type ApiResponse<T> = {\n  data: T;\n  message?: string;\n  success: boolean;\n};\n\nexport type PaginatedResponse<T> = ApiResponse<{\n  items: T[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}>;\n\nexport type ApiError = {\n  error: string;\n  message?: string;\n  statusCode?: number;\n};\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEA,MAAM,YAAY,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD,EAAE;IAC/B,SAAS;IACT,gBAAgB,OAAO;QACrB,MAAM,UAAU,MAAM,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;QAC/B,IAAI,SAAS,aAAa;YACxB,QAAQ,GAAG,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,WAAW,EAAE;QAC9D;QACA,OAAO;IACT;AACF;AAEO,MAAM,UAAU,CAAA,GAAA,kNAAA,CAAA,YAAS,AAAD,EAAE;IAC/B,aAAa;IACb;IACA,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW,IAAM,CAAC,CAAC,CAAC;AACtB", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/api/auth.ts"], "sourcesContent": ["import { baseApi, ApiResponse } from './base';\n\nexport interface User {\n  id: string;\n  name: string;\n  email: string;\n  avatar?: string;\n  role: 'admin' | 'project_manager' | 'translator' | 'reviewer' | 'client';\n  status: 'active' | 'inactive' | 'suspended' | 'pending';\n  organizationId: string;\n  languages: string[];\n  specializations: string[];\n  joinedAt: string;\n  lastActiveAt?: string;\n  preferences: UserPreferences;\n}\n\nexport interface UserPreferences {\n  language: string;\n  timezone: string;\n  notifications: {\n    email: boolean;\n    push: boolean;\n    projectUpdates: boolean;\n    comments: boolean;\n    deadlines: boolean;\n  };\n  editor: {\n    fontSize: number;\n    theme: 'light' | 'dark';\n    autoSave: boolean;\n    showTMMatches: boolean;\n    showTerminology: boolean;\n  };\n}\n\nexport interface Organization {\n  id: string;\n  name: string;\n  slug: string;\n  description?: string;\n  logo?: string;\n  plan: 'basic' | 'professional' | 'enterprise';\n  settings: OrganizationSettings;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface OrganizationSettings {\n  allowPublicProjects: boolean;\n  requireApproval: boolean;\n  defaultSourceLanguage: string;\n  supportedLanguages: string[];\n  qualityThreshold: number;\n  autoAssignment: boolean;\n}\n\nexport interface UpdateProfileRequest {\n  name?: string;\n  avatar?: string;\n  languages?: string[];\n  specializations?: string[];\n  preferences?: Partial<UserPreferences>;\n}\n\nexport interface ChangePasswordRequest {\n  currentPassword: string;\n  newPassword: string;\n}\n\nexport const authApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getCurrentUser: builder.query<ApiResponse<User>, void>({\n      query: () => '/auth/me',\n      providesTags: ['User'],\n    }),\n\n    updateProfile: builder.mutation<ApiResponse<User>, UpdateProfileRequest>({\n      query: (data) => ({\n        url: '/auth/profile',\n        method: 'PATCH',\n        body: data,\n      }),\n      invalidatesTags: ['User'],\n    }),\n\n    changePassword: builder.mutation<ApiResponse<void>, ChangePasswordRequest>({\n      query: (data) => ({\n        url: '/auth/change-password',\n        method: 'POST',\n        body: data,\n      }),\n    }),\n\n    uploadAvatar: builder.mutation<ApiResponse<{ avatarUrl: string }>, File>({\n      query: (file) => {\n        const formData = new FormData();\n        formData.append('avatar', file);\n        return {\n          url: '/auth/avatar',\n          method: 'POST',\n          body: formData,\n        };\n      },\n      invalidatesTags: ['User'],\n    }),\n\n    getOrganization: builder.query<ApiResponse<Organization>, void>({\n      query: () => '/auth/organization',\n      providesTags: ['Organization'],\n    }),\n\n    updateOrganization: builder.mutation<ApiResponse<Organization>, Partial<Organization>>({\n      query: (data) => ({\n        url: '/auth/organization',\n        method: 'PATCH',\n        body: data,\n      }),\n      invalidatesTags: ['Organization'],\n    }),\n\n    inviteUser: builder.mutation<ApiResponse<void>, {\n      email: string;\n      role: User['role'];\n      languages?: string[];\n    }>({\n      query: (data) => ({\n        url: '/auth/invite',\n        method: 'POST',\n        body: data,\n      }),\n    }),\n\n    acceptInvitation: builder.mutation<ApiResponse<User>, {\n      token: string;\n      name: string;\n      password: string;\n    }>({\n      query: (data) => ({\n        url: '/auth/accept-invitation',\n        method: 'POST',\n        body: data,\n      }),\n    }),\n\n    refreshToken: builder.mutation<ApiResponse<{ accessToken: string }>, void>({\n      query: () => ({\n        url: '/auth/refresh',\n        method: 'POST',\n      }),\n    }),\n\n    logout: builder.mutation<ApiResponse<void>, void>({\n      query: () => ({\n        url: '/auth/logout',\n        method: 'POST',\n      }),\n    }),\n  }),\n});\n\nexport const {\n  useGetCurrentUserQuery,\n  useUpdateProfileMutation,\n  useChangePasswordMutation,\n  useUploadAvatarMutation,\n  useGetOrganizationQuery,\n  useUpdateOrganizationMutation,\n  useInviteUserMutation,\n  useAcceptInvitationMutation,\n  useRefreshTokenMutation,\n  useLogoutMutation,\n} = authApi;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAsEO,MAAM,UAAU,yHAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IAC7C,WAAW,CAAC,UAAY,CAAC;YACvB,gBAAgB,QAAQ,KAAK,CAA0B;gBACrD,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAO;YACxB;YAEA,eAAe,QAAQ,QAAQ,CAA0C;gBACvE,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAO;YAC3B;YAEA,gBAAgB,QAAQ,QAAQ,CAA2C;gBACzE,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;YACH;YAEA,cAAc,QAAQ,QAAQ,CAA2C;gBACvE,OAAO,CAAC;oBACN,MAAM,WAAW,IAAI;oBACrB,SAAS,MAAM,CAAC,UAAU;oBAC1B,OAAO;wBACL,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR;gBACF;gBACA,iBAAiB;oBAAC;iBAAO;YAC3B;YAEA,iBAAiB,QAAQ,KAAK,CAAkC;gBAC9D,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAe;YAChC;YAEA,oBAAoB,QAAQ,QAAQ,CAAmD;gBACrF,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAe;YACnC;YAEA,YAAY,QAAQ,QAAQ,CAIzB;gBACD,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;YACH;YAEA,kBAAkB,QAAQ,QAAQ,CAI/B;gBACD,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;YACH;YAEA,cAAc,QAAQ,QAAQ,CAA6C;gBACzE,OAAO,IAAM,CAAC;wBACZ,KAAK;wBACL,QAAQ;oBACV,CAAC;YACH;YAEA,QAAQ,QAAQ,QAAQ,CAA0B;gBAChD,OAAO,IAAM,CAAC;wBACZ,KAAK;wBACL,QAAQ;oBACV,CAAC;YACH;QACF,CAAC;AACH;AAEO,MAAM,EACX,sBAAsB,EACtB,wBAAwB,EACxB,yBAAyB,EACzB,uBAAuB,EACvB,uBAAuB,EACvB,6BAA6B,EAC7B,qBAAqB,EACrB,2BAA2B,EAC3B,uBAAuB,EACvB,iBAAiB,EAClB,GAAG", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/api/projects.ts"], "sourcesContent": ["import { baseApi, ApiResponse, PaginatedResponse } from './base';\n\nexport interface Project {\n  id: string;\n  name: string;\n  description: string;\n  status: 'draft' | 'in_progress' | 'review' | 'completed' | 'on_hold';\n  priority: 'low' | 'medium' | 'high' | 'urgent';\n  sourceLanguage: string;\n  targetLanguages: string[];\n  createdAt: string;\n  updatedAt: string;\n  dueDate?: string;\n  budget?: number;\n  spent?: number;\n  totalSegments: number;\n  completedSegments: number;\n  reviewedSegments: number;\n  approvedSegments: number;\n  teamMembers: ProjectMember[];\n  files: ProjectFile[];\n  createdBy: string;\n  organizationId: string;\n}\n\nexport interface ProjectMember {\n  id: string;\n  userId: string;\n  projectId: string;\n  role: 'translator' | 'reviewer' | 'project_manager';\n  languages: string[];\n  assignedAt: string;\n  user: {\n    id: string;\n    name: string;\n    email: string;\n    avatar?: string;\n  };\n}\n\nexport interface ProjectFile {\n  id: string;\n  projectId: string;\n  name: string;\n  originalName: string;\n  size: number;\n  type: string;\n  url: string;\n  segments: number;\n  uploadedAt: string;\n  uploadedBy: string;\n}\n\nexport interface CreateProjectRequest {\n  name: string;\n  description?: string;\n  sourceLanguage: string;\n  targetLanguages: string[];\n  dueDate?: string;\n  budget?: number;\n  priority: Project['priority'];\n  teamMembers?: {\n    userId: string;\n    role: ProjectMember['role'];\n    languages: string[];\n  }[];\n}\n\nexport interface UpdateProjectRequest extends Partial<CreateProjectRequest> {\n  status?: Project['status'];\n}\n\nexport interface ProjectFilters {\n  status?: Project['status'];\n  priority?: Project['priority'];\n  sourceLanguage?: string;\n  targetLanguage?: string;\n  createdBy?: string;\n  search?: string;\n  page?: number;\n  limit?: number;\n}\n\nexport interface ProjectStats {\n  totalProjects: number;\n  activeProjects: number;\n  completedProjects: number;\n  totalSegments: number;\n  completedSegments: number;\n  totalBudget: number;\n  spentBudget: number;\n}\n\nexport const projectsApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getProjects: builder.query<PaginatedResponse<Project>, ProjectFilters>({\n      query: (filters = {}) => {\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach(([key, value]) => {\n          if (value !== undefined && value !== null && value !== '') {\n            params.append(key, value.toString());\n          }\n        });\n        return {\n          url: `projects?${params.toString()}`,\n          method: 'GET',\n        };\n      },\n      providesTags: (result) =>\n        result?.data.items\n          ? [\n              ...result.data.items.map(({ id }) => ({ type: 'Project' as const, id })),\n              { type: 'Project', id: 'LIST' },\n            ]\n          : [{ type: 'Project', id: 'LIST' }],\n    }),\n\n    getProjectStats: builder.query<ApiResponse<ProjectStats>, void>({\n      query: () => ({\n        url: 'projects/stats',\n        method: 'GET',\n      }),\n      providesTags: [{ type: 'Project', id: 'STATS' }],\n    }),\n\n    createProject: builder.mutation<ApiResponse<Project>, CreateProjectRequest>({\n      query: (projectData) => ({\n        url: 'projects',\n        method: 'POST',\n        body: projectData,\n      }),\n      invalidatesTags: [\n        { type: 'Project', id: 'LIST' },\n        { type: 'Project', id: 'STATS' },\n      ],\n    }),\n\n    getProject: builder.query<ApiResponse<Project>, string>({\n      query: (id) => ({\n        url: `projects/${id}`,\n        method: 'GET',\n      }),\n      providesTags: (result, error, id) => [{ type: 'Project', id }],\n    }),\n\n    updateProject: builder.mutation<ApiResponse<Project>, { id: string; data: Partial<CreateProjectRequest> }>({\n      query: ({ id, data }) => ({\n        url: `projects/${id}`,\n        method: 'PATCH',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { id }) => [\n        { type: 'Project', id },\n        { type: 'Project', id: 'LIST' },\n        { type: 'Project', id: 'STATS' },\n      ],\n    }),\n\n    deleteProject: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `projects/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: (result, error, id) => [\n        { type: 'Project', id },\n        { type: 'Project', id: 'LIST' },\n        { type: 'Project', id: 'STATS' },\n      ],\n    }),\n\n    addProjectMember: builder.mutation<ApiResponse<ProjectMember>, {\n      projectId: string;\n      userId: string;\n      role: ProjectMember['role'];\n      languages: string[];\n    }>({\n      query: ({ projectId, ...data }) => ({\n        url: `/projects/${projectId}/members`,\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { projectId }) => [{ type: 'Project', id: projectId }],\n    }),\n\n    removeProjectMember: builder.mutation<ApiResponse<void>, {\n      projectId: string;\n      memberId: string;\n    }>({\n      query: ({ projectId, memberId }) => ({\n        url: `/projects/${projectId}/members/${memberId}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: (result, error, { projectId }) => [{ type: 'Project', id: projectId }],\n    }),\n\n    uploadProjectFile: builder.mutation<ApiResponse<ProjectFile>, {\n      projectId: string;\n      file: File;\n    }>({\n      query: ({ projectId, file }) => {\n        const formData = new FormData();\n        formData.append('file', file);\n        return {\n          url: `/projects/${projectId}/files`,\n          method: 'POST',\n          body: formData,\n        };\n      },\n      invalidatesTags: (result, error, { projectId }) => [{ type: 'Project', id: projectId }],\n    }),\n\n    deleteProjectFile: builder.mutation<ApiResponse<void>, {\n      projectId: string;\n      fileId: string;\n    }>({\n      query: ({ projectId, fileId }) => ({\n        url: `/projects/${projectId}/files/${fileId}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: (result, error, { projectId }) => [{ type: 'Project', id: projectId }],\n    }),\n\n    getProjectStats: builder.query<ApiResponse<{\n      totalProjects: number;\n      activeProjects: number;\n      completedProjects: number;\n      totalSegments: number;\n      completedSegments: number;\n      totalBudget: number;\n      spentBudget: number;\n    }>, void>({\n      query: () => '/projects/stats',\n      providesTags: ['Project'],\n    }),\n  }),\n});\n\nexport const {\n  useGetProjectsQuery,\n  useGetProjectStatsQuery,\n  useCreateProjectMutation,\n  useGetProjectQuery,\n  useUpdateProjectMutation,\n  useDeleteProjectMutation,\n} = projectsApi;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AA6FO,MAAM,cAAc,yHAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IACjD,WAAW,CAAC,UAAY,CAAC;YACvB,aAAa,QAAQ,KAAK,CAA6C;gBACrE,OAAO,CAAC,UAAU,CAAC,CAAC;oBAClB,MAAM,SAAS,IAAI;oBACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;wBAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;4BACzD,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;wBACnC;oBACF;oBACA,OAAO;wBACL,KAAK,CAAC,SAAS,EAAE,OAAO,QAAQ,IAAI;wBACpC,QAAQ;oBACV;gBACF;gBACA,cAAc,CAAC,SACb,QAAQ,KAAK,QACT;2BACK,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,GAAK,CAAC;gCAAE,MAAM;gCAAoB;4BAAG,CAAC;wBACtE;4BAAE,MAAM;4BAAW,IAAI;wBAAO;qBAC/B,GACD;wBAAC;4BAAE,MAAM;4BAAW,IAAI;wBAAO;qBAAE;YACzC;YAEA,iBAAiB,QAAQ,KAAK,CAAkC;gBAC9D,OAAO,IAAM,CAAC;wBACZ,KAAK;wBACL,QAAQ;oBACV,CAAC;gBACD,cAAc;oBAAC;wBAAE,MAAM;wBAAW,IAAI;oBAAQ;iBAAE;YAClD;YAEA,eAAe,QAAQ,QAAQ,CAA6C;gBAC1E,OAAO,CAAC,cAAgB,CAAC;wBACvB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBACf;wBAAE,MAAM;wBAAW,IAAI;oBAAO;oBAC9B;wBAAE,MAAM;wBAAW,IAAI;oBAAQ;iBAChC;YACH;YAEA,YAAY,QAAQ,KAAK,CAA+B;gBACtD,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,SAAS,EAAE,IAAI;wBACrB,QAAQ;oBACV,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,KAAO;wBAAC;4BAAE,MAAM;4BAAW;wBAAG;qBAAE;YAChE;YAEA,eAAe,QAAQ,QAAQ,CAA4E;gBACzG,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,SAAS,EAAE,IAAI;wBACrB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,EAAE,EAAE,GAAK;wBAC1C;4BAAE,MAAM;4BAAW;wBAAG;wBACtB;4BAAE,MAAM;4BAAW,IAAI;wBAAO;wBAC9B;4BAAE,MAAM;4BAAW,IAAI;wBAAQ;qBAChC;YACH;YAEA,eAAe,QAAQ,QAAQ,CAA4B;gBACzD,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,SAAS,EAAE,IAAI;wBACrB,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,KAAO;wBACtC;4BAAE,MAAM;4BAAW;wBAAG;wBACtB;4BAAE,MAAM;4BAAW,IAAI;wBAAO;wBAC9B;4BAAE,MAAM;4BAAW,IAAI;wBAAQ;qBAChC;YACH;YAEA,kBAAkB,QAAQ,QAAQ,CAK/B;gBACD,OAAO,CAAC,EAAE,SAAS,EAAE,GAAG,MAAM,GAAK,CAAC;wBAClC,KAAK,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC;wBACrC,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,SAAS,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAW,IAAI;wBAAU;qBAAE;YACzF;YAEA,qBAAqB,QAAQ,QAAQ,CAGlC;gBACD,OAAO,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAK,CAAC;wBACnC,KAAK,CAAC,UAAU,EAAE,UAAU,SAAS,EAAE,UAAU;wBACjD,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,SAAS,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAW,IAAI;wBAAU;qBAAE;YACzF;YAEA,mBAAmB,QAAQ,QAAQ,CAGhC;gBACD,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;oBACzB,MAAM,WAAW,IAAI;oBACrB,SAAS,MAAM,CAAC,QAAQ;oBACxB,OAAO;wBACL,KAAK,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC;wBACnC,QAAQ;wBACR,MAAM;oBACR;gBACF;gBACA,iBAAiB,CAAC,QAAQ,OAAO,EAAE,SAAS,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAW,IAAI;wBAAU;qBAAE;YACzF;YAEA,mBAAmB,QAAQ,QAAQ,CAGhC;gBACD,OAAO,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,GAAK,CAAC;wBACjC,KAAK,CAAC,UAAU,EAAE,UAAU,OAAO,EAAE,QAAQ;wBAC7C,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,SAAS,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAW,IAAI;wBAAU;qBAAE;YACzF;YAEA,iBAAiB,QAAQ,KAAK,CAQpB;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAU;YAC3B;QACF,CAAC;AACH;AAEO,MAAM,EACX,mBAAmB,EACnB,uBAAuB,EACvB,wBAAwB,EACxB,kBAAkB,EAClB,wBAAwB,EACxB,wBAAwB,EACzB,GAAG", "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/api/terminology.ts"], "sourcesContent": ["import { baseApi, ApiResponse, PaginatedResponse } from './base';\n\nexport interface TerminologyEntry {\n  id: string;\n  sourceTerm: string;\n  targetTerm: string;\n  targetLanguage: string;\n  category: string;\n  context?: string;\n  usageNotes?: string;\n  approvalStatus: 'pending' | 'approved' | 'rejected';\n  frequency: number;\n  createdBy: string;\n  reviewedBy?: string;\n  lastUsed?: string;\n  createdAt: string;\n  updatedAt: string;\n  organizationId: string;\n  projectId?: string;\n}\n\nexport interface CreateTerminologyRequest {\n  sourceTerm: string;\n  targetTerm: string;\n  targetLanguage: string;\n  category: string;\n  context?: string;\n  usageNotes?: string;\n  projectId?: string;\n}\n\nexport interface UpdateTerminologyRequest extends Partial<CreateTerminologyRequest> {\n  approvalStatus?: TerminologyEntry['approvalStatus'];\n}\n\nexport interface TerminologyFilters {\n  search?: string;\n  category?: string;\n  targetLanguage?: string;\n  approvalStatus?: TerminologyEntry['approvalStatus'];\n  projectId?: string;\n  createdBy?: string;\n  page?: number;\n  limit?: number;\n}\n\nexport interface ImportTerminologyRequest {\n  file: File;\n  format: 'csv' | 'tmx' | 'excel';\n  mapping: Record<string, string>;\n  overwriteExisting: boolean;\n}\n\nexport interface ImportResult {\n  imported: number;\n  errors: number;\n  duplicates: number;\n  total: number;\n  errorDetails?: Array<{\n    row: number;\n    error: string;\n    data: any;\n  }>;\n}\n\nexport const terminologyApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getTerminology: builder.query<PaginatedResponse<TerminologyEntry>, TerminologyFilters>({\n      query: (filters = {}) => {\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach(([key, value]) => {\n          if (value !== undefined && value !== null && value !== '') {\n            params.append(key, value.toString());\n          }\n        });\n        return {\n          url: `terminology?${params.toString()}`,\n          method: 'GET',\n        };\n      },\n      providesTags: (result) =>\n        result?.data.items\n          ? [\n              ...result.data.items.map(({ id }) => ({ type: 'Terminology' as const, id })),\n              { type: 'Terminology', id: 'LIST' },\n            ]\n          : [{ type: 'Terminology', id: 'LIST' }],\n    }),\n\n    getTerminologyStats: builder.query<ApiResponse<{\n      total: number;\n      approved: number;\n      pending: number;\n      rejected: number;\n      byLanguage: Record<string, number>;\n      byCategory: Record<string, number>;\n    }>, void>({\n      query: () => ({\n        url: 'terminology/stats',\n        method: 'GET',\n      }),\n      providesTags: [{ type: 'Terminology', id: 'STATS' }],\n    }),\n\n    getTerminologyEntry: builder.query<ApiResponse<TerminologyEntry>, string>({\n      query: (id) => ({\n        url: `terminology/${id}`,\n        method: 'GET',\n      }),\n      providesTags: (result, error, id) => [{ type: 'Terminology', id }],\n    }),\n\n    createTerminologyEntry: builder.mutation<ApiResponse<TerminologyEntry>, CreateTerminologyRequest>({\n      query: (data) => ({\n        url: 'terminology',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: [\n        { type: 'Terminology', id: 'LIST' },\n        { type: 'Terminology', id: 'STATS' },\n      ],\n    }),\n\n    updateTerminologyEntry: builder.mutation<ApiResponse<TerminologyEntry>, {\n      id: string;\n      data: UpdateTerminologyRequest;\n    }>({\n      query: ({ id, data }) => ({\n        url: `terminology/${id}`,\n        method: 'PATCH',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { id }) => [\n        { type: 'Terminology', id },\n        { type: 'Terminology', id: 'LIST' },\n        { type: 'Terminology', id: 'STATS' },\n      ],\n    }),\n\n    deleteTerminologyEntry: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `terminology/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: (result, error, id) => [\n        { type: 'Terminology', id },\n        { type: 'Terminology', id: 'LIST' },\n        { type: 'Terminology', id: 'STATS' },\n      ],\n    }),\n\n    approveTerminologyEntry: builder.mutation<ApiResponse<TerminologyEntry>, string>({\n      query: (id) => ({\n        url: `terminology/${id}/approve`,\n        method: 'POST',\n      }),\n      invalidatesTags: (result, error, id) => [\n        { type: 'Terminology', id },\n        { type: 'Terminology', id: 'LIST' },\n        { type: 'Terminology', id: 'STATS' },\n      ],\n    }),\n\n    rejectTerminologyEntry: builder.mutation<ApiResponse<TerminologyEntry>, string>({\n      query: (id) => ({\n        url: `terminology/${id}/reject`,\n        method: 'POST',\n      }),\n      invalidatesTags: (result, error, id) => [\n        { type: 'Terminology', id },\n        { type: 'Terminology', id: 'LIST' },\n        { type: 'Terminology', id: 'STATS' },\n      ],\n    }),\n\n    importTerminology: builder.mutation<ApiResponse<ImportResult>, ImportTerminologyRequest>({\n      query: ({ file, format, mapping, overwriteExisting }) => {\n        const formData = new FormData();\n        formData.append('file', file);\n        formData.append('format', format);\n        formData.append('mapping', JSON.stringify(mapping));\n        formData.append('overwriteExisting', overwriteExisting.toString());\n        return {\n          url: '/terminology/import',\n          method: 'POST',\n          body: formData,\n        };\n      },\n      invalidatesTags: ['Terminology'],\n    }),\n\n    exportTerminology: builder.mutation<Blob, {\n      format: 'csv' | 'tmx' | 'excel';\n      filters?: TerminologyFilters;\n    }>({\n      query: ({ format, filters }) => ({\n        url: '/terminology/export',\n        method: 'POST',\n        body: { format, filters },\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    searchTerminology: builder.query<ApiResponse<TerminologyEntry[]>, {\n      query: string;\n      targetLanguage?: string;\n      limit?: number;\n    }>({\n      query: ({ query, targetLanguage, limit = 10 }) => ({\n        url: '/terminology/search',\n        params: { q: query, targetLanguage, limit },\n      }),\n      providesTags: ['Terminology'],\n    }),\n\n    getTerminologyStats: builder.query<ApiResponse<{\n      total: number;\n      approved: number;\n      pending: number;\n      rejected: number;\n      byLanguage: Record<string, number>;\n      byCategory: Record<string, number>;\n    }>, void>({\n      query: () => '/terminology/stats',\n      providesTags: ['Terminology'],\n    }),\n\n    getTerminologyForProject: builder.query<ApiResponse<TerminologyEntry[]>, {\n      projectId: string;\n      sourceText?: string;\n    }>({\n      query: ({ projectId, sourceText }) => ({\n        url: `/terminology/project/${projectId}`,\n        params: sourceText ? { sourceText } : undefined,\n      }),\n      providesTags: (result, error, { projectId }) => [\n        { type: 'Terminology', id: `project-${projectId}` }\n      ],\n    }),\n  }),\n});\n\nexport const {\n  useGetTerminologyQuery,\n  useGetTerminologyStatsQuery,\n  useGetTerminologyEntryQuery,\n  useCreateTerminologyEntryMutation,\n  useUpdateTerminologyEntryMutation,\n  useDeleteTerminologyEntryMutation,\n  useApproveTerminologyEntryMutation,\n  useRejectTerminologyEntryMutation,\n  useImportTerminologyMutation,\n  useExportTerminologyMutation,\n  useSearchTerminologyQuery,\n  useGetTerminologyForProjectQuery,\n} = terminologyApi;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAiEO,MAAM,iBAAiB,yHAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IACpD,WAAW,CAAC,UAAY,CAAC;YACvB,gBAAgB,QAAQ,KAAK,CAA0D;gBACrF,OAAO,CAAC,UAAU,CAAC,CAAC;oBAClB,MAAM,SAAS,IAAI;oBACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;wBAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;4BACzD,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;wBACnC;oBACF;oBACA,OAAO;wBACL,KAAK,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;wBACvC,QAAQ;oBACV;gBACF;gBACA,cAAc,CAAC,SACb,QAAQ,KAAK,QACT;2BACK,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,GAAK,CAAC;gCAAE,MAAM;gCAAwB;4BAAG,CAAC;wBAC1E;4BAAE,MAAM;4BAAe,IAAI;wBAAO;qBACnC,GACD;wBAAC;4BAAE,MAAM;4BAAe,IAAI;wBAAO;qBAAE;YAC7C;YAEA,qBAAqB,QAAQ,KAAK,CAOxB;gBACR,OAAO,IAAM,CAAC;wBACZ,KAAK;wBACL,QAAQ;oBACV,CAAC;gBACD,cAAc;oBAAC;wBAAE,MAAM;wBAAe,IAAI;oBAAQ;iBAAE;YACtD;YAEA,qBAAqB,QAAQ,KAAK,CAAwC;gBACxE,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,YAAY,EAAE,IAAI;wBACxB,QAAQ;oBACV,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,KAAO;wBAAC;4BAAE,MAAM;4BAAe;wBAAG;qBAAE;YACpE;YAEA,wBAAwB,QAAQ,QAAQ,CAA0D;gBAChG,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBACf;wBAAE,MAAM;wBAAe,IAAI;oBAAO;oBAClC;wBAAE,MAAM;wBAAe,IAAI;oBAAQ;iBACpC;YACH;YAEA,wBAAwB,QAAQ,QAAQ,CAGrC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,YAAY,EAAE,IAAI;wBACxB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,EAAE,EAAE,GAAK;wBAC1C;4BAAE,MAAM;4BAAe;wBAAG;wBAC1B;4BAAE,MAAM;4BAAe,IAAI;wBAAO;wBAClC;4BAAE,MAAM;4BAAe,IAAI;wBAAQ;qBACpC;YACH;YAEA,wBAAwB,QAAQ,QAAQ,CAA4B;gBAClE,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,YAAY,EAAE,IAAI;wBACxB,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,KAAO;wBACtC;4BAAE,MAAM;4BAAe;wBAAG;wBAC1B;4BAAE,MAAM;4BAAe,IAAI;wBAAO;wBAClC;4BAAE,MAAM;4BAAe,IAAI;wBAAQ;qBACpC;YACH;YAEA,yBAAyB,QAAQ,QAAQ,CAAwC;gBAC/E,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,YAAY,EAAE,GAAG,QAAQ,CAAC;wBAChC,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,KAAO;wBACtC;4BAAE,MAAM;4BAAe;wBAAG;wBAC1B;4BAAE,MAAM;4BAAe,IAAI;wBAAO;wBAClC;4BAAE,MAAM;4BAAe,IAAI;wBAAQ;qBACpC;YACH;YAEA,wBAAwB,QAAQ,QAAQ,CAAwC;gBAC9E,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,YAAY,EAAE,GAAG,OAAO,CAAC;wBAC/B,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,KAAO;wBACtC;4BAAE,MAAM;4BAAe;wBAAG;wBAC1B;4BAAE,MAAM;4BAAe,IAAI;wBAAO;wBAClC;4BAAE,MAAM;4BAAe,IAAI;wBAAQ;qBACpC;YACH;YAEA,mBAAmB,QAAQ,QAAQ,CAAsD;gBACvF,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE;oBAClD,MAAM,WAAW,IAAI;oBACrB,SAAS,MAAM,CAAC,QAAQ;oBACxB,SAAS,MAAM,CAAC,UAAU;oBAC1B,SAAS,MAAM,CAAC,WAAW,KAAK,SAAS,CAAC;oBAC1C,SAAS,MAAM,CAAC,qBAAqB,kBAAkB,QAAQ;oBAC/D,OAAO;wBACL,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR;gBACF;gBACA,iBAAiB;oBAAC;iBAAc;YAClC;YAEA,mBAAmB,QAAQ,QAAQ,CAGhC;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAK,CAAC;wBAC/B,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAQ;wBAAQ;wBACxB,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,mBAAmB,QAAQ,KAAK,CAI7B;gBACD,OAAO,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,EAAE,GAAK,CAAC;wBACjD,KAAK;wBACL,QAAQ;4BAAE,GAAG;4BAAO;4BAAgB;wBAAM;oBAC5C,CAAC;gBACD,cAAc;oBAAC;iBAAc;YAC/B;YAEA,qBAAqB,QAAQ,KAAK,CAOxB;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAc;YAC/B;YAEA,0BAA0B,QAAQ,KAAK,CAGpC;gBACD,OAAO,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,GAAK,CAAC;wBACrC,KAAK,CAAC,qBAAqB,EAAE,WAAW;wBACxC,QAAQ,aAAa;4BAAE;wBAAW,IAAI;oBACxC,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,EAAE,SAAS,EAAE,GAAK;wBAC9C;4BAAE,MAAM;4BAAe,IAAI,CAAC,QAAQ,EAAE,WAAW;wBAAC;qBACnD;YACH;QACF,CAAC;AACH;AAEO,MAAM,EACX,sBAAsB,EACtB,2BAA2B,EAC3B,2BAA2B,EAC3B,iCAAiC,EACjC,iCAAiC,EACjC,iCAAiC,EACjC,kCAAkC,EAClC,iCAAiC,EACjC,4BAA4B,EAC5B,4BAA4B,EAC5B,yBAAyB,EACzB,gCAAgC,EACjC,GAAG", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/api/users.ts"], "sourcesContent": ["import { baseApi, ApiResponse, PaginatedResponse } from './base';\nimport { User } from './auth';\n\nexport interface UserStats {\n  projectsCompleted: number;\n  wordsTranslated: number;\n  averageRating: number;\n  currentProjects: number;\n  totalEarnings: number;\n  productivity: {\n    wordsPerHour: number;\n    segmentsPerHour: number;\n  };\n  qualityMetrics: {\n    averageScore: number;\n    errorRate: number;\n    revisionRate: number;\n  };\n}\n\nexport interface UserFilters {\n  search?: string;\n  role?: User['role'];\n  status?: User['status'];\n  languages?: string[];\n  organizationId?: string;\n  page?: number;\n  limit?: number;\n}\n\nexport interface CreateUserRequest {\n  name: string;\n  email: string;\n  role: User['role'];\n  languages: string[];\n  specializations?: string[];\n  organizationId?: string;\n}\n\nexport interface UpdateUserRequest extends Partial<CreateUserRequest> {\n  status?: User['status'];\n}\n\nexport interface UserActivity {\n  id: string;\n  userId: string;\n  type: 'login' | 'logout' | 'project_created' | 'translation_completed' | 'review_completed';\n  description: string;\n  metadata?: Record<string, any>;\n  timestamp: string;\n}\n\nexport const usersApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getUsers: builder.query<PaginatedResponse<User>, UserFilters>({\n      query: (filters) => ({\n        url: '/users',\n        params: filters,\n      }),\n      providesTags: ['User'],\n    }),\n\n    getUser: builder.query<ApiResponse<User>, string>({\n      query: (id) => `/users/${id}`,\n      providesTags: (result, error, id) => [{ type: 'User', id }],\n    }),\n\n    createUser: builder.mutation<ApiResponse<User>, CreateUserRequest>({\n      query: (data) => ({\n        url: '/users',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['User'],\n    }),\n\n    updateUser: builder.mutation<ApiResponse<User>, {\n      id: string;\n      data: UpdateUserRequest;\n    }>({\n      query: ({ id, data }) => ({\n        url: `/users/${id}`,\n        method: 'PATCH',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { id }) => [{ type: 'User', id }],\n    }),\n\n    deleteUser: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/users/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['User'],\n    }),\n\n    suspendUser: builder.mutation<ApiResponse<User>, string>({\n      query: (id) => ({\n        url: `/users/${id}/suspend`,\n        method: 'POST',\n      }),\n      invalidatesTags: (result, error, id) => [{ type: 'User', id }],\n    }),\n\n    activateUser: builder.mutation<ApiResponse<User>, string>({\n      query: (id) => ({\n        url: `/users/${id}/activate`,\n        method: 'POST',\n      }),\n      invalidatesTags: (result, error, id) => [{ type: 'User', id }],\n    }),\n\n    getUserStats: builder.query<ApiResponse<UserStats>, string>({\n      query: (id) => `/users/${id}/stats`,\n      providesTags: (result, error, id) => [{ type: 'User', id: `stats-${id}` }],\n    }),\n\n    getUserActivity: builder.query<PaginatedResponse<UserActivity>, {\n      userId: string;\n      page?: number;\n      limit?: number;\n    }>({\n      query: ({ userId, page, limit }) => ({\n        url: `/users/${userId}/activity`,\n        params: { page, limit },\n      }),\n      providesTags: (result, error, { userId }) => [{ type: 'User', id: `activity-${userId}` }],\n    }),\n\n    getTeamMembers: builder.query<ApiResponse<User[]>, {\n      organizationId?: string;\n      projectId?: string;\n    }>({\n      query: (params) => ({\n        url: '/users/team',\n        params,\n      }),\n      providesTags: ['User', 'Team'],\n    }),\n\n    searchUsers: builder.query<ApiResponse<User[]>, {\n      query: string;\n      role?: User['role'];\n      languages?: string[];\n      limit?: number;\n    }>({\n      query: ({ query, role, languages, limit = 10 }) => ({\n        url: '/users/search',\n        params: { q: query, role, languages: languages?.join(','), limit },\n      }),\n      providesTags: ['User'],\n    }),\n\n    getUserPerformance: builder.query<ApiResponse<{\n      productivity: {\n        daily: Array<{ date: string; words: number; segments: number }>;\n        weekly: Array<{ week: string; words: number; segments: number }>;\n        monthly: Array<{ month: string; words: number; segments: number }>;\n      };\n      quality: {\n        scores: Array<{ date: string; score: number }>;\n        trends: {\n          improving: boolean;\n          averageScore: number;\n          changePercent: number;\n        };\n      };\n      projects: Array<{\n        id: string;\n        name: string;\n        role: string;\n        progress: number;\n        deadline: string;\n      }>;\n    }>, {\n      userId: string;\n      period?: 'week' | 'month' | 'quarter' | 'year';\n    }>({\n      query: ({ userId, period = 'month' }) => ({\n        url: `/users/${userId}/performance`,\n        params: { period },\n      }),\n      providesTags: (result, error, { userId }) => [{ type: 'User', id: `performance-${userId}` }],\n    }),\n\n    bulkUpdateUsers: builder.mutation<ApiResponse<void>, {\n      userIds: string[];\n      data: Partial<UpdateUserRequest>;\n    }>({\n      query: ({ userIds, data }) => ({\n        url: '/users/bulk-update',\n        method: 'POST',\n        body: { userIds, ...data },\n      }),\n      invalidatesTags: ['User'],\n    }),\n\n    exportUsers: builder.mutation<Blob, {\n      format: 'csv' | 'excel';\n      filters?: UserFilters;\n    }>({\n      query: ({ format, filters }) => ({\n        url: '/users/export',\n        method: 'POST',\n        body: { format, filters },\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n  }),\n});\n\nexport const {\n  useGetUsersQuery,\n  useGetUserQuery,\n  useCreateUserMutation,\n  useUpdateUserMutation,\n  useDeleteUserMutation,\n  useSuspendUserMutation,\n  useActivateUserMutation,\n  useGetUserStatsQuery,\n  useGetUserActivityQuery,\n  useGetTeamMembersQuery,\n  useSearchUsersQuery,\n  useGetUserPerformanceQuery,\n  useBulkUpdateUsersMutation,\n  useExportUsersMutation,\n} = usersApi;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;AAoDO,MAAM,WAAW,yHAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IAC9C,WAAW,CAAC,UAAY,CAAC;YACvB,UAAU,QAAQ,KAAK,CAAuC;gBAC5D,OAAO,CAAC,UAAY,CAAC;wBACnB,KAAK;wBACL,QAAQ;oBACV,CAAC;gBACD,cAAc;oBAAC;iBAAO;YACxB;YAEA,SAAS,QAAQ,KAAK,CAA4B;gBAChD,OAAO,CAAC,KAAO,CAAC,OAAO,EAAE,IAAI;gBAC7B,cAAc,CAAC,QAAQ,OAAO,KAAO;wBAAC;4BAAE,MAAM;4BAAQ;wBAAG;qBAAE;YAC7D;YAEA,YAAY,QAAQ,QAAQ,CAAuC;gBACjE,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAO;YAC3B;YAEA,YAAY,QAAQ,QAAQ,CAGzB;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,OAAO,EAAE,IAAI;wBACnB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,EAAE,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAQ;wBAAG;qBAAE;YACpE;YAEA,YAAY,QAAQ,QAAQ,CAA4B;gBACtD,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,OAAO,EAAE,IAAI;wBACnB,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAO;YAC3B;YAEA,aAAa,QAAQ,QAAQ,CAA4B;gBACvD,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC;wBAC3B,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,KAAO;wBAAC;4BAAE,MAAM;4BAAQ;wBAAG;qBAAE;YAChE;YAEA,cAAc,QAAQ,QAAQ,CAA4B;gBACxD,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;wBAC5B,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,KAAO;wBAAC;4BAAE,MAAM;4BAAQ;wBAAG;qBAAE;YAChE;YAEA,cAAc,QAAQ,KAAK,CAAiC;gBAC1D,OAAO,CAAC,KAAO,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC;gBACnC,cAAc,CAAC,QAAQ,OAAO,KAAO;wBAAC;4BAAE,MAAM;4BAAQ,IAAI,CAAC,MAAM,EAAE,IAAI;wBAAC;qBAAE;YAC5E;YAEA,iBAAiB,QAAQ,KAAK,CAI3B;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAK,CAAC;wBACnC,KAAK,CAAC,OAAO,EAAE,OAAO,SAAS,CAAC;wBAChC,QAAQ;4BAAE;4BAAM;wBAAM;oBACxB,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,EAAE,MAAM,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAQ,IAAI,CAAC,SAAS,EAAE,QAAQ;wBAAC;qBAAE;YAC3F;YAEA,gBAAgB,QAAQ,KAAK,CAG1B;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;oBAAQ;iBAAO;YAChC;YAEA,aAAa,QAAQ,KAAK,CAKvB;gBACD,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,EAAE,GAAK,CAAC;wBAClD,KAAK;wBACL,QAAQ;4BAAE,GAAG;4BAAO;4BAAM,WAAW,WAAW,KAAK;4BAAM;wBAAM;oBACnE,CAAC;gBACD,cAAc;oBAAC;iBAAO;YACxB;YAEA,oBAAoB,QAAQ,KAAK,CAwB9B;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,SAAS,OAAO,EAAE,GAAK,CAAC;wBACxC,KAAK,CAAC,OAAO,EAAE,OAAO,YAAY,CAAC;wBACnC,QAAQ;4BAAE;wBAAO;oBACnB,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,EAAE,MAAM,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAQ,IAAI,CAAC,YAAY,EAAE,QAAQ;wBAAC;qBAAE;YAC9F;YAEA,iBAAiB,QAAQ,QAAQ,CAG9B;gBACD,OAAO,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,GAAK,CAAC;wBAC7B,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAS,GAAG,IAAI;wBAAC;oBAC3B,CAAC;gBACD,iBAAiB;oBAAC;iBAAO;YAC3B;YAEA,aAAa,QAAQ,QAAQ,CAG1B;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAK,CAAC;wBAC/B,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAQ;wBAAQ;wBACxB,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;QACF,CAAC;AACH;AAEO,MAAM,EACX,gBAAgB,EAChB,eAAe,EACf,qBAAqB,EACrB,qBAAqB,EACrB,qBAAqB,EACrB,sBAAsB,EACtB,uBAAuB,EACvB,oBAAoB,EACpB,uBAAuB,EACvB,sBAAsB,EACtB,mBAAmB,EACnB,0BAA0B,EAC1B,0BAA0B,EAC1B,sBAAsB,EACvB,GAAG", "debugId": null}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/api/translation.ts"], "sourcesContent": ["import { baseApi, ApiResponse, PaginatedResponse } from './base';\n\nexport interface TranslationSegment {\n  id: string;\n  projectId: string;\n  fileId: string;\n  segmentNumber: number;\n  sourceText: string;\n  targetText: string;\n  status: 'pending' | 'in_progress' | 'completed' | 'review' | 'approved';\n  translatedBy?: string;\n  reviewedBy?: string;\n  approvedBy?: string;\n  translatedAt?: string;\n  reviewedAt?: string;\n  approvedAt?: string;\n  comments: Comment[];\n  tmMatches: TMMatch[];\n  qualityScore?: number;\n  wordCount: number;\n  characterCount: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Comment {\n  id: string;\n  segmentId: string;\n  authorId: string;\n  content: string;\n  type: 'comment' | 'issue' | 'suggestion';\n  status?: 'open' | 'resolved';\n  createdAt: string;\n  updatedAt: string;\n  author: {\n    id: string;\n    name: string;\n    avatar?: string;\n    role: string;\n  };\n  replies?: Comment[];\n}\n\nexport interface TMMatch {\n  id: string;\n  sourceText: string;\n  targetText: string;\n  similarity: number;\n  origin: string;\n  projectId?: string;\n  createdAt: string;\n}\n\nexport interface UpdateSegmentRequest {\n  targetText: string;\n  status?: TranslationSegment['status'];\n}\n\nexport interface CreateCommentRequest {\n  content: string;\n  type: Comment['type'];\n  parentId?: string;\n}\n\nexport interface SegmentFilters {\n  projectId?: string;\n  fileId?: string;\n  status?: TranslationSegment['status'];\n  translatedBy?: string;\n  reviewedBy?: string;\n  search?: string;\n  page?: number;\n  limit?: number;\n}\n\nexport interface TranslationStats {\n  totalSegments: number;\n  completedSegments: number;\n  reviewedSegments: number;\n  approvedSegments: number;\n  pendingSegments: number;\n  totalWords: number;\n  completedWords: number;\n  averageQualityScore: number;\n  productivity: {\n    segmentsPerHour: number;\n    wordsPerHour: number;\n  };\n}\n\nexport const translationApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getSegments: builder.query<PaginatedResponse<TranslationSegment>, SegmentFilters>({\n      query: (filters) => ({\n        url: '/translation/segments',\n        params: filters,\n      }),\n      providesTags: ['Translation'],\n    }),\n\n    getSegment: builder.query<ApiResponse<TranslationSegment>, string>({\n      query: (id) => `/translation/segments/${id}`,\n      providesTags: (result, error, id) => [{ type: 'Translation', id }],\n    }),\n\n    updateSegment: builder.mutation<ApiResponse<TranslationSegment>, {\n      id: string;\n      data: UpdateSegmentRequest;\n    }>({\n      query: ({ id, data }) => ({\n        url: `/translation/segments/${id}`,\n        method: 'PATCH',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { id }) => [{ type: 'Translation', id }],\n    }),\n\n    getSegmentComments: builder.query<ApiResponse<Comment[]>, string>({\n      query: (segmentId) => `/translation/segments/${segmentId}/comments`,\n      providesTags: (result, error, segmentId) => [{ type: 'Comment', id: segmentId }],\n    }),\n\n    addSegmentComment: builder.mutation<ApiResponse<Comment>, {\n      segmentId: string;\n      data: CreateCommentRequest;\n    }>({\n      query: ({ segmentId, data }) => ({\n        url: `/translation/segments/${segmentId}/comments`,\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { segmentId }) => [{ type: 'Comment', id: segmentId }],\n    }),\n\n    updateComment: builder.mutation<ApiResponse<Comment>, {\n      commentId: string;\n      data: Partial<CreateCommentRequest>;\n    }>({\n      query: ({ commentId, data }) => ({\n        url: `/translation/comments/${commentId}`,\n        method: 'PATCH',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { commentId }) => [{ type: 'Comment', id: commentId }],\n    }),\n\n    deleteComment: builder.mutation<ApiResponse<void>, string>({\n      query: (commentId) => ({\n        url: `/translation/comments/${commentId}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['Comment'],\n    }),\n\n    getTMMatches: builder.query<ApiResponse<TMMatch[]>, {\n      sourceText: string;\n      targetLanguage: string;\n      projectId?: string;\n      threshold?: number;\n    }>({\n      query: ({ sourceText, targetLanguage, projectId, threshold = 70 }) => ({\n        url: '/translation/tm-matches',\n        params: { sourceText, targetLanguage, projectId, threshold },\n      }),\n    }),\n\n    getTranslationStats: builder.query<ApiResponse<TranslationStats>, {\n      projectId?: string;\n      userId?: string;\n      dateFrom?: string;\n      dateTo?: string;\n    }>({\n      query: (params) => ({\n        url: '/translation/stats',\n        params,\n      }),\n      providesTags: ['Translation'],\n    }),\n\n    assignSegments: builder.mutation<ApiResponse<void>, {\n      segmentIds: string[];\n      userId: string;\n    }>({\n      query: (data) => ({\n        url: '/translation/assign',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['Translation'],\n    }),\n\n    bulkUpdateSegments: builder.mutation<ApiResponse<void>, {\n      segmentIds: string[];\n      data: Partial<UpdateSegmentRequest>;\n    }>({\n      query: ({ segmentIds, data }) => ({\n        url: '/translation/bulk-update',\n        method: 'POST',\n        body: { segmentIds, ...data },\n      }),\n      invalidatesTags: ['Translation'],\n    }),\n\n    exportTranslation: builder.mutation<Blob, {\n      projectId: string;\n      format: 'tmx' | 'xliff' | 'csv';\n      includeApprovedOnly?: boolean;\n    }>({\n      query: ({ projectId, format, includeApprovedOnly = false }) => ({\n        url: `/translation/export/${projectId}`,\n        method: 'POST',\n        body: { format, includeApprovedOnly },\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    getProjectProgress: builder.query<ApiResponse<{\n      overall: number;\n      byLanguage: Record<string, number>;\n      byFile: Record<string, number>;\n      byStatus: Record<string, number>;\n    }>, string>({\n      query: (projectId) => `/translation/progress/${projectId}`,\n      providesTags: (result, error, projectId) => [{ type: 'Translation', id: `progress-${projectId}` }],\n    }),\n  }),\n});\n\nexport const {\n  useGetSegmentsQuery,\n  useGetSegmentQuery,\n  useUpdateSegmentMutation,\n  useGetSegmentCommentsQuery,\n  useAddSegmentCommentMutation,\n  useUpdateCommentMutation,\n  useDeleteCommentMutation,\n  useGetTMMatchesQuery,\n  useGetTranslationStatsQuery,\n  useAssignSegmentsMutation,\n  useBulkUpdateSegmentsMutation,\n  useExportTranslationMutation,\n  useGetProjectProgressQuery,\n} = translationApi;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AA0FO,MAAM,iBAAiB,yHAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IACpD,WAAW,CAAC,UAAY,CAAC;YACvB,aAAa,QAAQ,KAAK,CAAwD;gBAChF,OAAO,CAAC,UAAY,CAAC;wBACnB,KAAK;wBACL,QAAQ;oBACV,CAAC;gBACD,cAAc;oBAAC;iBAAc;YAC/B;YAEA,YAAY,QAAQ,KAAK,CAA0C;gBACjE,OAAO,CAAC,KAAO,CAAC,sBAAsB,EAAE,IAAI;gBAC5C,cAAc,CAAC,QAAQ,OAAO,KAAO;wBAAC;4BAAE,MAAM;4BAAe;wBAAG;qBAAE;YACpE;YAEA,eAAe,QAAQ,QAAQ,CAG5B;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,sBAAsB,EAAE,IAAI;wBAClC,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,EAAE,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAe;wBAAG;qBAAE;YAC3E;YAEA,oBAAoB,QAAQ,KAAK,CAAiC;gBAChE,OAAO,CAAC,YAAc,CAAC,sBAAsB,EAAE,UAAU,SAAS,CAAC;gBACnE,cAAc,CAAC,QAAQ,OAAO,YAAc;wBAAC;4BAAE,MAAM;4BAAW,IAAI;wBAAU;qBAAE;YAClF;YAEA,mBAAmB,QAAQ,QAAQ,CAGhC;gBACD,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAK,CAAC;wBAC/B,KAAK,CAAC,sBAAsB,EAAE,UAAU,SAAS,CAAC;wBAClD,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,SAAS,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAW,IAAI;wBAAU;qBAAE;YACzF;YAEA,eAAe,QAAQ,QAAQ,CAG5B;gBACD,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAK,CAAC;wBAC/B,KAAK,CAAC,sBAAsB,EAAE,WAAW;wBACzC,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,SAAS,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAW,IAAI;wBAAU;qBAAE;YACzF;YAEA,eAAe,QAAQ,QAAQ,CAA4B;gBACzD,OAAO,CAAC,YAAc,CAAC;wBACrB,KAAK,CAAC,sBAAsB,EAAE,WAAW;wBACzC,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAU;YAC9B;YAEA,cAAc,QAAQ,KAAK,CAKxB;gBACD,OAAO,CAAC,EAAE,UAAU,EAAE,cAAc,EAAE,SAAS,EAAE,YAAY,EAAE,EAAE,GAAK,CAAC;wBACrE,KAAK;wBACL,QAAQ;4BAAE;4BAAY;4BAAgB;4BAAW;wBAAU;oBAC7D,CAAC;YACH;YAEA,qBAAqB,QAAQ,KAAK,CAK/B;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAc;YAC/B;YAEA,gBAAgB,QAAQ,QAAQ,CAG7B;gBACD,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAc;YAClC;YAEA,oBAAoB,QAAQ,QAAQ,CAGjC;gBACD,OAAO,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,GAAK,CAAC;wBAChC,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAY,GAAG,IAAI;wBAAC;oBAC9B,CAAC;gBACD,iBAAiB;oBAAC;iBAAc;YAClC;YAEA,mBAAmB,QAAQ,QAAQ,CAIhC;gBACD,OAAO,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,sBAAsB,KAAK,EAAE,GAAK,CAAC;wBAC9D,KAAK,CAAC,oBAAoB,EAAE,WAAW;wBACvC,QAAQ;wBACR,MAAM;4BAAE;4BAAQ;wBAAoB;wBACpC,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,oBAAoB,QAAQ,KAAK,CAKrB;gBACV,OAAO,CAAC,YAAc,CAAC,sBAAsB,EAAE,WAAW;gBAC1D,cAAc,CAAC,QAAQ,OAAO,YAAc;wBAAC;4BAAE,MAAM;4BAAe,IAAI,CAAC,SAAS,EAAE,WAAW;wBAAC;qBAAE;YACpG;QACF,CAAC;AACH;AAEO,MAAM,EACX,mBAAmB,EACnB,kBAAkB,EAClB,wBAAwB,EACxB,0BAA0B,EAC1B,4BAA4B,EAC5B,wBAAwB,EACxB,wBAAwB,EACxB,oBAAoB,EACpB,2BAA2B,EAC3B,yBAAyB,EACzB,6BAA6B,EAC7B,4BAA4B,EAC5B,0BAA0B,EAC3B,GAAG", "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/api/store.ts"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit';\nimport { setupListeners } from '@reduxjs/toolkit/query';\nimport { baseApi } from './base';\n\n// Import the API slices to ensure they're injected into baseApi\nimport './auth';\nimport './projects';\nimport './terminology';\nimport './users';\nimport './translation';\n\nexport const store = configureStore({\n  reducer: {\n    [baseApi.reducerPath]: baseApi.reducer,\n  },\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware().concat(baseApi.middleware),\n});\n\nsetupListeners(store.dispatch);\n\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAEA,gEAAgE;AAChE;AACA;AACA;AACA;AACA;;;;;;;;;AAEO,MAAM,QAAQ,CAAA,GAAA,2LAAA,CAAA,iBAAc,AAAD,EAAE;IAClC,SAAS;QACP,CAAC,yHAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE,yHAAA,CAAA,UAAO,CAAC,OAAO;IACxC;IACA,YAAY,CAAC,uBACX,uBAAuB,MAAM,CAAC,yHAAA,CAAA,UAAO,CAAC,UAAU;AACpD;AAEA,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,QAAQ", "debugId": null}}, {"offset": {"line": 982, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/providers/ReduxProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { Provider } from 'react-redux';\nimport { store } from '@/lib/api/store';\n\ninterface ReduxProviderProps {\n  children: React.ReactNode;\n}\n\nexport function ReduxProvider({ children }: ReduxProviderProps) {\n  return <Provider store={store}>{children}</Provider>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,qBAAO,8OAAC,yJAAA,CAAA,WAAQ;QAAC,OAAO,0HAAA,CAAA,QAAK;kBAAG;;;;;;AAClC", "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/store/hooks.ts"], "sourcesContent": ["import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';\nimport type { RootState, AppDispatch } from './index';\n\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\n"], "names": [], "mappings": ";;;;AAAA;;AAIO,MAAM,iBAAiB,IAAM,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;AACvC,MAAM,iBAAkD,yJAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/store/slices/authSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\nimport { User } from '@/types';\n\ninterface AuthState {\n  user: User | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  currentOrganizationId: string | null;\n}\n\nconst initialState: AuthState = {\n  user: null,\n  isLoading: true,\n  isAuthenticated: false,\n  currentOrganizationId: null,\n};\n\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    setUser: (state, action: PayloadAction<User | null>) => {\n      state.user = action.payload;\n      state.isAuthenticated = !!action.payload;\n      state.isLoading = false;\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.isLoading = action.payload;\n    },\n    setCurrentOrganization: (state, action: PayloadAction<string | null>) => {\n      state.currentOrganizationId = action.payload;\n    },\n    logout: (state) => {\n      state.user = null;\n      state.isAuthenticated = false;\n      state.currentOrganizationId = null;\n      state.isLoading = false;\n    },\n  },\n});\n\nexport const { setUser, setLoading, setCurrentOrganization, logout } = authSlice.actions;\nexport default authSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAUA,MAAM,eAA0B;IAC9B,MAAM;IACN,WAAW;IACX,iBAAiB;IACjB,uBAAuB;AACzB;AAEA,MAAM,YAAY,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,SAAS,CAAC,OAAO;YACf,MAAM,IAAI,GAAG,OAAO,OAAO;YAC3B,MAAM,eAAe,GAAG,CAAC,CAAC,OAAO,OAAO;YACxC,MAAM,SAAS,GAAG;QACpB;QACA,YAAY,CAAC,OAAO;YAClB,MAAM,SAAS,GAAG,OAAO,OAAO;QAClC;QACA,wBAAwB,CAAC,OAAO;YAC9B,MAAM,qBAAqB,GAAG,OAAO,OAAO;QAC9C;QACA,QAAQ,CAAC;YACP,MAAM,IAAI,GAAG;YACb,MAAM,eAAe,GAAG;YACxB,MAAM,qBAAqB,GAAG;YAC9B,MAAM,SAAS,GAAG;QACpB;IACF;AACF;AAEO,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,sBAAsB,EAAE,MAAM,EAAE,GAAG,UAAU,OAAO;uCACzE,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 1068, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/providers/AuthProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { SessionProvider } from 'next-auth/react';\nimport { useSession } from 'next-auth/react';\nimport { useEffect } from 'react';\nimport { useAppDispatch } from '@/store/hooks';\nimport { setUser, setLoading } from '@/store/slices/authSlice';\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\nfunction AuthStateManager({ children }: { children: React.ReactNode }) {\n  const { data: session, status } = useSession();\n  const dispatch = useAppDispatch();\n\n  useEffect(() => {\n    if (status === 'loading') {\n      dispatch(setLoading(true));\n    } else {\n      dispatch(setLoading(false));\n      \n      if (session?.user) {\n        dispatch(setUser({\n          id: session.user.id || '',\n          email: session.user.email || '',\n          name: session.user.name || null,\n          avatar_url: session.user.image || null,\n          email_verified: true, // Assume verified if session exists\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n        }));\n      } else {\n        dispatch(setUser(null));\n      }\n    }\n  }, [session, status, dispatch]);\n\n  return <>{children}</>;\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  return (\n    <SessionProvider>\n      <AuthStateManager>\n        {children}\n      </AuthStateManager>\n    </SessionProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AANA;;;;;;;AAYA,SAAS,iBAAiB,EAAE,QAAQ,EAAiC;IACnE,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAE9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,WAAW;YACxB,SAAS,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;QACtB,OAAO;YACL,SAAS,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;YAEpB,IAAI,SAAS,MAAM;gBACjB,SAAS,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD,EAAE;oBACf,IAAI,QAAQ,IAAI,CAAC,EAAE,IAAI;oBACvB,OAAO,QAAQ,IAAI,CAAC,KAAK,IAAI;oBAC7B,MAAM,QAAQ,IAAI,CAAC,IAAI,IAAI;oBAC3B,YAAY,QAAQ,IAAI,CAAC,KAAK,IAAI;oBAClC,gBAAgB;oBAChB,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC;YACF,OAAO;gBACL,SAAS,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD,EAAE;YACnB;QACF;IACF,GAAG;QAAC;QAAS;QAAQ;KAAS;IAE9B,qBAAO;kBAAG;;AACZ;AAEO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,qBACE,8OAAC,8IAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC;sBACE;;;;;;;;;;;AAIT", "debugId": null}}]}