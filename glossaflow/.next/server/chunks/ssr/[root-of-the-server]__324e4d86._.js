module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/api/base.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "baseApi": (()=>baseApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$react$2f$rtk$2d$query$2d$react$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/react/index.js [app-ssr] (ecmascript)");
;
;
const baseQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fetchBaseQuery"])({
    baseUrl: '/api',
    prepareHeaders: async (headers)=>{
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getSession"])();
        if (session?.accessToken) {
            headers.set('authorization', `Bearer ${session.accessToken}`);
        }
        return headers;
    }
});
const baseApi = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$react$2f$rtk$2d$query$2d$react$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createApi"])({
    reducerPath: 'api',
    baseQuery,
    tagTypes: [
        'User',
        'Project',
        'Terminology',
        'Translation',
        'Comment',
        'File',
        'Team',
        'Organization'
    ],
    endpoints: ()=>({})
});
}}),
"[project]/src/lib/api/auth.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authApi": (()=>authApi),
    "useAcceptInvitationMutation": (()=>useAcceptInvitationMutation),
    "useChangePasswordMutation": (()=>useChangePasswordMutation),
    "useGetCurrentUserQuery": (()=>useGetCurrentUserQuery),
    "useGetOrganizationQuery": (()=>useGetOrganizationQuery),
    "useInviteUserMutation": (()=>useInviteUserMutation),
    "useLogoutMutation": (()=>useLogoutMutation),
    "useRefreshTokenMutation": (()=>useRefreshTokenMutation),
    "useUpdateOrganizationMutation": (()=>useUpdateOrganizationMutation),
    "useUpdateProfileMutation": (()=>useUpdateProfileMutation),
    "useUploadAvatarMutation": (()=>useUploadAvatarMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/base.ts [app-ssr] (ecmascript)");
;
const authApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    endpoints: (builder)=>({
            getCurrentUser: builder.query({
                query: ()=>'/auth/me',
                providesTags: [
                    'User'
                ]
            }),
            updateProfile: builder.mutation({
                query: (data)=>({
                        url: '/auth/profile',
                        method: 'PATCH',
                        body: data
                    }),
                invalidatesTags: [
                    'User'
                ]
            }),
            changePassword: builder.mutation({
                query: (data)=>({
                        url: '/auth/change-password',
                        method: 'POST',
                        body: data
                    })
            }),
            uploadAvatar: builder.mutation({
                query: (file)=>{
                    const formData = new FormData();
                    formData.append('avatar', file);
                    return {
                        url: '/auth/avatar',
                        method: 'POST',
                        body: formData
                    };
                },
                invalidatesTags: [
                    'User'
                ]
            }),
            getOrganization: builder.query({
                query: ()=>'/auth/organization',
                providesTags: [
                    'Organization'
                ]
            }),
            updateOrganization: builder.mutation({
                query: (data)=>({
                        url: '/auth/organization',
                        method: 'PATCH',
                        body: data
                    }),
                invalidatesTags: [
                    'Organization'
                ]
            }),
            inviteUser: builder.mutation({
                query: (data)=>({
                        url: '/auth/invite',
                        method: 'POST',
                        body: data
                    })
            }),
            acceptInvitation: builder.mutation({
                query: (data)=>({
                        url: '/auth/accept-invitation',
                        method: 'POST',
                        body: data
                    })
            }),
            refreshToken: builder.mutation({
                query: ()=>({
                        url: '/auth/refresh',
                        method: 'POST'
                    })
            }),
            logout: builder.mutation({
                query: ()=>({
                        url: '/auth/logout',
                        method: 'POST'
                    })
            })
        })
});
const { useGetCurrentUserQuery, useUpdateProfileMutation, useChangePasswordMutation, useUploadAvatarMutation, useGetOrganizationQuery, useUpdateOrganizationMutation, useInviteUserMutation, useAcceptInvitationMutation, useRefreshTokenMutation, useLogoutMutation } = authApi;
}}),
"[project]/src/lib/api/projects.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "projectsApi": (()=>projectsApi),
    "useCreateProjectMutation": (()=>useCreateProjectMutation),
    "useDeleteProjectMutation": (()=>useDeleteProjectMutation),
    "useGetProjectQuery": (()=>useGetProjectQuery),
    "useGetProjectStatsQuery": (()=>useGetProjectStatsQuery),
    "useGetProjectsQuery": (()=>useGetProjectsQuery),
    "useUpdateProjectMutation": (()=>useUpdateProjectMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/base.ts [app-ssr] (ecmascript)");
;
const projectsApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    endpoints: (builder)=>({
            getProjects: builder.query({
                query: (filters = {})=>{
                    const params = new URLSearchParams();
                    Object.entries(filters).forEach(([key, value])=>{
                        if (value !== undefined && value !== null && value !== '') {
                            params.append(key, value.toString());
                        }
                    });
                    return {
                        url: `projects?${params.toString()}`,
                        method: 'GET'
                    };
                },
                providesTags: (result)=>result?.data.items ? [
                        ...result.data.items.map(({ id })=>({
                                type: 'Project',
                                id
                            })),
                        {
                            type: 'Project',
                            id: 'LIST'
                        }
                    ] : [
                        {
                            type: 'Project',
                            id: 'LIST'
                        }
                    ]
            }),
            getProjectStats: builder.query({
                query: ()=>({
                        url: 'projects/stats',
                        method: 'GET'
                    }),
                providesTags: [
                    {
                        type: 'Project',
                        id: 'STATS'
                    }
                ]
            }),
            createProject: builder.mutation({
                query: (projectData)=>({
                        url: 'projects',
                        method: 'POST',
                        body: projectData
                    }),
                invalidatesTags: [
                    {
                        type: 'Project',
                        id: 'LIST'
                    },
                    {
                        type: 'Project',
                        id: 'STATS'
                    }
                ]
            }),
            getProject: builder.query({
                query: (id)=>({
                        url: `projects/${id}`,
                        method: 'GET'
                    }),
                providesTags: (result, error, id)=>[
                        {
                            type: 'Project',
                            id
                        }
                    ]
            }),
            updateProject: builder.mutation({
                query: ({ id, data })=>({
                        url: `projects/${id}`,
                        method: 'PATCH',
                        body: data
                    }),
                invalidatesTags: (result, error, { id })=>[
                        {
                            type: 'Project',
                            id
                        },
                        {
                            type: 'Project',
                            id: 'LIST'
                        },
                        {
                            type: 'Project',
                            id: 'STATS'
                        }
                    ]
            }),
            deleteProject: builder.mutation({
                query: (id)=>({
                        url: `projects/${id}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: (result, error, id)=>[
                        {
                            type: 'Project',
                            id
                        },
                        {
                            type: 'Project',
                            id: 'LIST'
                        },
                        {
                            type: 'Project',
                            id: 'STATS'
                        }
                    ]
            }),
            addProjectMember: builder.mutation({
                query: ({ projectId, ...data })=>({
                        url: `/projects/${projectId}/members`,
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: (result, error, { projectId })=>[
                        {
                            type: 'Project',
                            id: projectId
                        }
                    ]
            }),
            removeProjectMember: builder.mutation({
                query: ({ projectId, memberId })=>({
                        url: `/projects/${projectId}/members/${memberId}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: (result, error, { projectId })=>[
                        {
                            type: 'Project',
                            id: projectId
                        }
                    ]
            }),
            uploadProjectFile: builder.mutation({
                query: ({ projectId, file })=>{
                    const formData = new FormData();
                    formData.append('file', file);
                    return {
                        url: `/projects/${projectId}/files`,
                        method: 'POST',
                        body: formData
                    };
                },
                invalidatesTags: (result, error, { projectId })=>[
                        {
                            type: 'Project',
                            id: projectId
                        }
                    ]
            }),
            deleteProjectFile: builder.mutation({
                query: ({ projectId, fileId })=>({
                        url: `/projects/${projectId}/files/${fileId}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: (result, error, { projectId })=>[
                        {
                            type: 'Project',
                            id: projectId
                        }
                    ]
            }),
            getProjectStats: builder.query({
                query: ()=>'/projects/stats',
                providesTags: [
                    'Project'
                ]
            })
        })
});
const { useGetProjectsQuery, useGetProjectStatsQuery, useCreateProjectMutation, useGetProjectQuery, useUpdateProjectMutation, useDeleteProjectMutation } = projectsApi;
}}),
"[project]/src/lib/api/terminology.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "terminologyApi": (()=>terminologyApi),
    "useApproveTerminologyEntryMutation": (()=>useApproveTerminologyEntryMutation),
    "useCreateTerminologyEntryMutation": (()=>useCreateTerminologyEntryMutation),
    "useDeleteTerminologyEntryMutation": (()=>useDeleteTerminologyEntryMutation),
    "useExportTerminologyMutation": (()=>useExportTerminologyMutation),
    "useGetTerminologyEntryQuery": (()=>useGetTerminologyEntryQuery),
    "useGetTerminologyForProjectQuery": (()=>useGetTerminologyForProjectQuery),
    "useGetTerminologyQuery": (()=>useGetTerminologyQuery),
    "useGetTerminologyStatsQuery": (()=>useGetTerminologyStatsQuery),
    "useImportTerminologyMutation": (()=>useImportTerminologyMutation),
    "useRejectTerminologyEntryMutation": (()=>useRejectTerminologyEntryMutation),
    "useSearchTerminologyQuery": (()=>useSearchTerminologyQuery),
    "useUpdateTerminologyEntryMutation": (()=>useUpdateTerminologyEntryMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/base.ts [app-ssr] (ecmascript)");
;
const terminologyApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    endpoints: (builder)=>({
            getTerminology: builder.query({
                query: (filters)=>({
                        url: '/terminology',
                        params: filters
                    }),
                providesTags: [
                    'Terminology'
                ]
            }),
            getTerminologyEntry: builder.query({
                query: (id)=>`/terminology/${id}`,
                providesTags: (result, error, id)=>[
                        {
                            type: 'Terminology',
                            id
                        }
                    ]
            }),
            createTerminologyEntry: builder.mutation({
                query: (data)=>({
                        url: '/terminology',
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: [
                    'Terminology'
                ]
            }),
            updateTerminologyEntry: builder.mutation({
                query: ({ id, data })=>({
                        url: `/terminology/${id}`,
                        method: 'PATCH',
                        body: data
                    }),
                invalidatesTags: (result, error, { id })=>[
                        {
                            type: 'Terminology',
                            id
                        }
                    ]
            }),
            deleteTerminologyEntry: builder.mutation({
                query: (id)=>({
                        url: `/terminology/${id}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: [
                    'Terminology'
                ]
            }),
            approveTerminologyEntry: builder.mutation({
                query: (id)=>({
                        url: `/terminology/${id}/approve`,
                        method: 'POST'
                    }),
                invalidatesTags: (result, error, id)=>[
                        {
                            type: 'Terminology',
                            id
                        }
                    ]
            }),
            rejectTerminologyEntry: builder.mutation({
                query: (id)=>({
                        url: `/terminology/${id}/reject`,
                        method: 'POST'
                    }),
                invalidatesTags: (result, error, id)=>[
                        {
                            type: 'Terminology',
                            id
                        }
                    ]
            }),
            importTerminology: builder.mutation({
                query: ({ file, format, mapping, overwriteExisting })=>{
                    const formData = new FormData();
                    formData.append('file', file);
                    formData.append('format', format);
                    formData.append('mapping', JSON.stringify(mapping));
                    formData.append('overwriteExisting', overwriteExisting.toString());
                    return {
                        url: '/terminology/import',
                        method: 'POST',
                        body: formData
                    };
                },
                invalidatesTags: [
                    'Terminology'
                ]
            }),
            exportTerminology: builder.mutation({
                query: ({ format, filters })=>({
                        url: '/terminology/export',
                        method: 'POST',
                        body: {
                            format,
                            filters
                        },
                        responseHandler: (response)=>response.blob()
                    })
            }),
            searchTerminology: builder.query({
                query: ({ query, targetLanguage, limit = 10 })=>({
                        url: '/terminology/search',
                        params: {
                            q: query,
                            targetLanguage,
                            limit
                        }
                    }),
                providesTags: [
                    'Terminology'
                ]
            }),
            getTerminologyStats: builder.query({
                query: ()=>'/terminology/stats',
                providesTags: [
                    'Terminology'
                ]
            }),
            getTerminologyForProject: builder.query({
                query: ({ projectId, sourceText })=>({
                        url: `/terminology/project/${projectId}`,
                        params: sourceText ? {
                            sourceText
                        } : undefined
                    }),
                providesTags: (result, error, { projectId })=>[
                        {
                            type: 'Terminology',
                            id: `project-${projectId}`
                        }
                    ]
            })
        })
});
const { useGetTerminologyQuery, useGetTerminologyEntryQuery, useCreateTerminologyEntryMutation, useUpdateTerminologyEntryMutation, useDeleteTerminologyEntryMutation, useApproveTerminologyEntryMutation, useRejectTerminologyEntryMutation, useImportTerminologyMutation, useExportTerminologyMutation, useSearchTerminologyQuery, useGetTerminologyStatsQuery, useGetTerminologyForProjectQuery } = terminologyApi;
}}),
"[project]/src/lib/api/users.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useActivateUserMutation": (()=>useActivateUserMutation),
    "useBulkUpdateUsersMutation": (()=>useBulkUpdateUsersMutation),
    "useCreateUserMutation": (()=>useCreateUserMutation),
    "useDeleteUserMutation": (()=>useDeleteUserMutation),
    "useExportUsersMutation": (()=>useExportUsersMutation),
    "useGetTeamMembersQuery": (()=>useGetTeamMembersQuery),
    "useGetUserActivityQuery": (()=>useGetUserActivityQuery),
    "useGetUserPerformanceQuery": (()=>useGetUserPerformanceQuery),
    "useGetUserQuery": (()=>useGetUserQuery),
    "useGetUserStatsQuery": (()=>useGetUserStatsQuery),
    "useGetUsersQuery": (()=>useGetUsersQuery),
    "useSearchUsersQuery": (()=>useSearchUsersQuery),
    "useSuspendUserMutation": (()=>useSuspendUserMutation),
    "useUpdateUserMutation": (()=>useUpdateUserMutation),
    "usersApi": (()=>usersApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/base.ts [app-ssr] (ecmascript)");
;
const usersApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    endpoints: (builder)=>({
            getUsers: builder.query({
                query: (filters)=>({
                        url: '/users',
                        params: filters
                    }),
                providesTags: [
                    'User'
                ]
            }),
            getUser: builder.query({
                query: (id)=>`/users/${id}`,
                providesTags: (result, error, id)=>[
                        {
                            type: 'User',
                            id
                        }
                    ]
            }),
            createUser: builder.mutation({
                query: (data)=>({
                        url: '/users',
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: [
                    'User'
                ]
            }),
            updateUser: builder.mutation({
                query: ({ id, data })=>({
                        url: `/users/${id}`,
                        method: 'PATCH',
                        body: data
                    }),
                invalidatesTags: (result, error, { id })=>[
                        {
                            type: 'User',
                            id
                        }
                    ]
            }),
            deleteUser: builder.mutation({
                query: (id)=>({
                        url: `/users/${id}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: [
                    'User'
                ]
            }),
            suspendUser: builder.mutation({
                query: (id)=>({
                        url: `/users/${id}/suspend`,
                        method: 'POST'
                    }),
                invalidatesTags: (result, error, id)=>[
                        {
                            type: 'User',
                            id
                        }
                    ]
            }),
            activateUser: builder.mutation({
                query: (id)=>({
                        url: `/users/${id}/activate`,
                        method: 'POST'
                    }),
                invalidatesTags: (result, error, id)=>[
                        {
                            type: 'User',
                            id
                        }
                    ]
            }),
            getUserStats: builder.query({
                query: (id)=>`/users/${id}/stats`,
                providesTags: (result, error, id)=>[
                        {
                            type: 'User',
                            id: `stats-${id}`
                        }
                    ]
            }),
            getUserActivity: builder.query({
                query: ({ userId, page, limit })=>({
                        url: `/users/${userId}/activity`,
                        params: {
                            page,
                            limit
                        }
                    }),
                providesTags: (result, error, { userId })=>[
                        {
                            type: 'User',
                            id: `activity-${userId}`
                        }
                    ]
            }),
            getTeamMembers: builder.query({
                query: (params)=>({
                        url: '/users/team',
                        params
                    }),
                providesTags: [
                    'User',
                    'Team'
                ]
            }),
            searchUsers: builder.query({
                query: ({ query, role, languages, limit = 10 })=>({
                        url: '/users/search',
                        params: {
                            q: query,
                            role,
                            languages: languages?.join(','),
                            limit
                        }
                    }),
                providesTags: [
                    'User'
                ]
            }),
            getUserPerformance: builder.query({
                query: ({ userId, period = 'month' })=>({
                        url: `/users/${userId}/performance`,
                        params: {
                            period
                        }
                    }),
                providesTags: (result, error, { userId })=>[
                        {
                            type: 'User',
                            id: `performance-${userId}`
                        }
                    ]
            }),
            bulkUpdateUsers: builder.mutation({
                query: ({ userIds, data })=>({
                        url: '/users/bulk-update',
                        method: 'POST',
                        body: {
                            userIds,
                            ...data
                        }
                    }),
                invalidatesTags: [
                    'User'
                ]
            }),
            exportUsers: builder.mutation({
                query: ({ format, filters })=>({
                        url: '/users/export',
                        method: 'POST',
                        body: {
                            format,
                            filters
                        },
                        responseHandler: (response)=>response.blob()
                    })
            })
        })
});
const { useGetUsersQuery, useGetUserQuery, useCreateUserMutation, useUpdateUserMutation, useDeleteUserMutation, useSuspendUserMutation, useActivateUserMutation, useGetUserStatsQuery, useGetUserActivityQuery, useGetTeamMembersQuery, useSearchUsersQuery, useGetUserPerformanceQuery, useBulkUpdateUsersMutation, useExportUsersMutation } = usersApi;
}}),
"[project]/src/lib/api/translation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "translationApi": (()=>translationApi),
    "useAddSegmentCommentMutation": (()=>useAddSegmentCommentMutation),
    "useAssignSegmentsMutation": (()=>useAssignSegmentsMutation),
    "useBulkUpdateSegmentsMutation": (()=>useBulkUpdateSegmentsMutation),
    "useDeleteCommentMutation": (()=>useDeleteCommentMutation),
    "useExportTranslationMutation": (()=>useExportTranslationMutation),
    "useGetProjectProgressQuery": (()=>useGetProjectProgressQuery),
    "useGetSegmentCommentsQuery": (()=>useGetSegmentCommentsQuery),
    "useGetSegmentQuery": (()=>useGetSegmentQuery),
    "useGetSegmentsQuery": (()=>useGetSegmentsQuery),
    "useGetTMMatchesQuery": (()=>useGetTMMatchesQuery),
    "useGetTranslationStatsQuery": (()=>useGetTranslationStatsQuery),
    "useUpdateCommentMutation": (()=>useUpdateCommentMutation),
    "useUpdateSegmentMutation": (()=>useUpdateSegmentMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/base.ts [app-ssr] (ecmascript)");
;
const translationApi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["baseApi"].injectEndpoints({
    endpoints: (builder)=>({
            getSegments: builder.query({
                query: (filters)=>({
                        url: '/translation/segments',
                        params: filters
                    }),
                providesTags: [
                    'Translation'
                ]
            }),
            getSegment: builder.query({
                query: (id)=>`/translation/segments/${id}`,
                providesTags: (result, error, id)=>[
                        {
                            type: 'Translation',
                            id
                        }
                    ]
            }),
            updateSegment: builder.mutation({
                query: ({ id, data })=>({
                        url: `/translation/segments/${id}`,
                        method: 'PATCH',
                        body: data
                    }),
                invalidatesTags: (result, error, { id })=>[
                        {
                            type: 'Translation',
                            id
                        }
                    ]
            }),
            getSegmentComments: builder.query({
                query: (segmentId)=>`/translation/segments/${segmentId}/comments`,
                providesTags: (result, error, segmentId)=>[
                        {
                            type: 'Comment',
                            id: segmentId
                        }
                    ]
            }),
            addSegmentComment: builder.mutation({
                query: ({ segmentId, data })=>({
                        url: `/translation/segments/${segmentId}/comments`,
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: (result, error, { segmentId })=>[
                        {
                            type: 'Comment',
                            id: segmentId
                        }
                    ]
            }),
            updateComment: builder.mutation({
                query: ({ commentId, data })=>({
                        url: `/translation/comments/${commentId}`,
                        method: 'PATCH',
                        body: data
                    }),
                invalidatesTags: (result, error, { commentId })=>[
                        {
                            type: 'Comment',
                            id: commentId
                        }
                    ]
            }),
            deleteComment: builder.mutation({
                query: (commentId)=>({
                        url: `/translation/comments/${commentId}`,
                        method: 'DELETE'
                    }),
                invalidatesTags: [
                    'Comment'
                ]
            }),
            getTMMatches: builder.query({
                query: ({ sourceText, targetLanguage, projectId, threshold = 70 })=>({
                        url: '/translation/tm-matches',
                        params: {
                            sourceText,
                            targetLanguage,
                            projectId,
                            threshold
                        }
                    })
            }),
            getTranslationStats: builder.query({
                query: (params)=>({
                        url: '/translation/stats',
                        params
                    }),
                providesTags: [
                    'Translation'
                ]
            }),
            assignSegments: builder.mutation({
                query: (data)=>({
                        url: '/translation/assign',
                        method: 'POST',
                        body: data
                    }),
                invalidatesTags: [
                    'Translation'
                ]
            }),
            bulkUpdateSegments: builder.mutation({
                query: ({ segmentIds, data })=>({
                        url: '/translation/bulk-update',
                        method: 'POST',
                        body: {
                            segmentIds,
                            ...data
                        }
                    }),
                invalidatesTags: [
                    'Translation'
                ]
            }),
            exportTranslation: builder.mutation({
                query: ({ projectId, format, includeApprovedOnly = false })=>({
                        url: `/translation/export/${projectId}`,
                        method: 'POST',
                        body: {
                            format,
                            includeApprovedOnly
                        },
                        responseHandler: (response)=>response.blob()
                    })
            }),
            getProjectProgress: builder.query({
                query: (projectId)=>`/translation/progress/${projectId}`,
                providesTags: (result, error, projectId)=>[
                        {
                            type: 'Translation',
                            id: `progress-${projectId}`
                        }
                    ]
            })
        })
});
const { useGetSegmentsQuery, useGetSegmentQuery, useUpdateSegmentMutation, useGetSegmentCommentsQuery, useAddSegmentCommentMutation, useUpdateCommentMutation, useDeleteCommentMutation, useGetTMMatchesQuery, useGetTranslationStatsQuery, useAssignSegmentsMutation, useBulkUpdateSegmentsMutation, useExportTranslationMutation, useGetProjectProgressQuery } = translationApi;
}}),
"[project]/src/lib/api/store.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "store": (()=>store)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/base.ts [app-ssr] (ecmascript)");
// Import the API slices to ensure they're injected into baseApi
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$auth$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/auth.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$projects$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/projects.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$terminology$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/terminology.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$users$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/users.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$translation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/translation.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
const store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["configureStore"])({
    reducer: {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["baseApi"].reducerPath]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["baseApi"].reducer
    },
    middleware: (getDefaultMiddleware)=>getDefaultMiddleware().concat(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["baseApi"].middleware)
});
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setupListeners"])(store.dispatch);
}}),
"[project]/src/components/providers/ReduxProvider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReduxProvider": (()=>ReduxProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/store.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
function ReduxProvider({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Provider"], {
        store: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["store"],
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/providers/ReduxProvider.tsx",
        lineNumber: 11,
        columnNumber: 10
    }, this);
}
}}),
"[project]/src/store/hooks.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAppDispatch": (()=>useAppDispatch),
    "useAppSelector": (()=>useAppSelector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-ssr] (ecmascript)");
;
const useAppDispatch = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDispatch"])();
const useAppSelector = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSelector"];
}}),
"[project]/src/store/slices/authSlice.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "logout": (()=>logout),
    "setCurrentOrganization": (()=>setCurrentOrganization),
    "setLoading": (()=>setLoading),
    "setUser": (()=>setUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
;
const initialState = {
    user: null,
    isLoading: true,
    isAuthenticated: false,
    currentOrganizationId: null
};
const authSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'auth',
    initialState,
    reducers: {
        setUser: (state, action)=>{
            state.user = action.payload;
            state.isAuthenticated = !!action.payload;
            state.isLoading = false;
        },
        setLoading: (state, action)=>{
            state.isLoading = action.payload;
        },
        setCurrentOrganization: (state, action)=>{
            state.currentOrganizationId = action.payload;
        },
        logout: (state)=>{
            state.user = null;
            state.isAuthenticated = false;
            state.currentOrganizationId = null;
            state.isLoading = false;
        }
    }
});
const { setUser, setLoading, setCurrentOrganization, logout } = authSlice.actions;
const __TURBOPACK__default__export__ = authSlice.reducer;
}}),
"[project]/src/components/providers/AuthProvider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/react/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/hooks.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/slices/authSlice.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
function AuthStateManager({ children }) {
    const { data: session, status } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSession"])();
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppDispatch"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (status === 'loading') {
            dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setLoading"])(true));
        } else {
            dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setLoading"])(false));
            if (session?.user) {
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setUser"])({
                    id: session.user.id || '',
                    email: session.user.email || '',
                    name: session.user.name || null,
                    avatar_url: session.user.image || null,
                    email_verified: true,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }));
            } else {
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setUser"])(null));
            }
        }
    }, [
        session,
        status,
        dispatch
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
function AuthProvider({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthStateManager, {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/providers/AuthProvider.tsx",
            lineNumber: 45,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/providers/AuthProvider.tsx",
        lineNumber: 44,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__324e4d86._.js.map