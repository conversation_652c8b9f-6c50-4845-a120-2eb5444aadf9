{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/accessibility/SkipLink.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface SkipLinkProps {\n  href: string;\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function SkipLink({ href, children, className = '' }: SkipLinkProps) {\n  const [isVisible, setIsVisible] = useState(false);\n\n  return (\n    <a\n      href={href}\n      className={`\n        fixed top-4 left-4 z-50 px-4 py-2 bg-blue-600 text-white rounded-md\n        transform transition-transform duration-200 ease-in-out\n        focus:translate-y-0 focus:opacity-100\n        ${isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'}\n        ${className}\n      `}\n      onFocus={() => setIsVisible(true)}\n      onBlur={() => setIsVisible(false)}\n    >\n      {children}\n    </a>\n  );\n}\n\nexport function SkipLinks() {\n  return (\n    <>\n      <SkipLink href=\"#main-content\">Skip to main content</SkipLink>\n      <SkipLink href=\"#navigation\">Skip to navigation</SkipLink>\n      <SkipLink href=\"#search\">Skip to search</SkipLink>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAUO,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAiB;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAC;;;;QAIV,EAAE,YAAY,8BAA8B,8BAA8B;QAC1E,EAAE,UAAU;MACd,CAAC;QACD,SAAS,IAAM,aAAa;QAC5B,QAAQ,IAAM,aAAa;kBAE1B;;;;;;AAGP;AAEO,SAAS;IACd,qBACE;;0BACE,8OAAC;gBAAS,MAAK;0BAAgB;;;;;;0BAC/B,8OAAC;gBAAS,MAAK;0BAAc;;;;;;0BAC7B,8OAAC;gBAAS,MAAK;0BAAU;;;;;;;;AAG/B", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/accessibility/ScreenReaderOnly.tsx"], "sourcesContent": ["interface ScreenReaderOnlyProps {\n  children: React.ReactNode;\n  as?: keyof JSX.IntrinsicElements;\n  className?: string;\n}\n\nexport function ScreenReaderOnly({ \n  children, \n  as: Component = 'span',\n  className = '' \n}: ScreenReaderOnlyProps) {\n  return (\n    <Component \n      className={`sr-only ${className}`}\n      aria-hidden=\"false\"\n    >\n      {children}\n    </Component>\n  );\n}\n\n// Utility component for live regions\ninterface LiveRegionProps {\n  children: React.ReactNode;\n  priority?: 'polite' | 'assertive';\n  atomic?: boolean;\n  relevant?: 'additions' | 'removals' | 'text' | 'all';\n}\n\nexport function LiveRegion({ \n  children, \n  priority = 'polite',\n  atomic = true,\n  relevant = 'all'\n}: LiveRegionProps) {\n  return (\n    <div\n      aria-live={priority}\n      aria-atomic={atomic}\n      aria-relevant={relevant}\n      className=\"sr-only\"\n    >\n      {children}\n    </div>\n  );\n}\n\n// Component for status messages\ninterface StatusMessageProps {\n  message: string;\n  type?: 'success' | 'error' | 'warning' | 'info';\n  visible?: boolean;\n}\n\nexport function StatusMessage({ \n  message, \n  type = 'info',\n  visible = true \n}: StatusMessageProps) {\n  if (!visible) return null;\n\n  const priority = type === 'error' ? 'assertive' : 'polite';\n\n  return (\n    <LiveRegion priority={priority}>\n      <span role=\"status\" aria-label={`${type}: ${message}`}>\n        {message}\n      </span>\n    </LiveRegion>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAMO,SAAS,iBAAiB,EAC/B,QAAQ,EACR,IAAI,YAAY,MAAM,EACtB,YAAY,EAAE,EACQ;IACtB,qBACE,8OAAC;QACC,WAAW,CAAC,QAAQ,EAAE,WAAW;QACjC,eAAY;kBAEX;;;;;;AAGP;AAUO,SAAS,WAAW,EACzB,QAAQ,EACR,WAAW,QAAQ,EACnB,SAAS,IAAI,EACb,WAAW,KAAK,EACA;IAChB,qBACE,8OAAC;QACC,aAAW;QACX,eAAa;QACb,iBAAe;QACf,WAAU;kBAET;;;;;;AAGP;AASO,SAAS,cAAc,EAC5B,OAAO,EACP,OAAO,MAAM,EACb,UAAU,IAAI,EACK;IACnB,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,WAAW,SAAS,UAAU,cAAc;IAElD,qBACE,8OAAC;QAAW,UAAU;kBACpB,cAAA,8OAAC;YAAK,MAAK;YAAS,cAAY,GAAG,KAAK,EAAE,EAAE,SAAS;sBAClD;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 712, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useSession, signOut } from 'next-auth/react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';\nimport { SkipLinks } from '@/components/accessibility/SkipLink';\nimport { ScreenReaderOnly } from '@/components/accessibility/ScreenReaderOnly';\nimport {\n  Home,\n  FolderOpen,\n  BookOpen,\n  Users,\n  Settings,\n  LogOut,\n  Menu,\n  Bell,\n  Search,\n  Plus,\n} from 'lucide-react';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: Home },\n  { name: 'Projects', href: '/dashboard/projects', icon: FolderOpen },\n  { name: 'Terminology', href: '/dashboard/terminology', icon: BookOpen },\n  { name: 'Team', href: '/dashboard/team', icon: Users },\n  { name: 'Settings', href: '/dashboard/settings', icon: Settings },\n];\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const { data: session } = useSession();\n  const pathname = usePathname();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <SkipLinks />\n\n      {/* Mobile sidebar */}\n      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>\n        <SheetContent side=\"left\" className=\"w-64 p-0\">\n          <div className=\"flex h-full flex-col\">\n            <div className=\"flex h-16 items-center px-6 border-b\">\n              <Link href=\"/dashboard\" className=\"flex items-center\">\n                <span className=\"text-xl font-bold text-blue-600\">GlossaFlow</span>\n                <ScreenReaderOnly>- Translation Management Platform</ScreenReaderOnly>\n              </Link>\n            </div>\n            <nav\n              className=\"flex-1 space-y-1 px-3 py-4\"\n              id=\"navigation\"\n              role=\"navigation\"\n              aria-label=\"Main navigation\"\n            >\n              {navigation.map((item) => {\n                const isActive = pathname === item.href;\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                      isActive\n                        ? 'bg-blue-100 text-blue-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    }`}\n                    onClick={() => setSidebarOpen(false)}\n                    aria-current={isActive ? 'page' : undefined}\n                  >\n                    <item.icon className=\"mr-3 h-5 w-5\" aria-hidden=\"true\" />\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n        </SheetContent>\n      </Sheet>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex min-h-0 flex-1 flex-col bg-white border-r border-gray-200\">\n          <div className=\"flex h-16 items-center px-6 border-b\">\n            <Link href=\"/dashboard\" className=\"flex items-center\">\n              <span className=\"text-xl font-bold text-blue-600\">GlossaFlow</span>\n            </Link>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-3 py-4\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                    isActive\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-700 hover:bg-gray-100'\n                  }`}\n                >\n                  <item.icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top navigation */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <SheetTrigger asChild>\n            <Button variant=\"ghost\" size=\"sm\" className=\"lg:hidden\">\n              <Menu className=\"h-5 w-5\" />\n            </Button>\n          </SheetTrigger>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1 items-center\">\n              <Search className=\"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 pl-3\" />\n              <input\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm\"\n                placeholder=\"Search projects, terminology...\"\n                type=\"search\"\n              />\n            </div>\n            <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n              <Button size=\"sm\" className=\"hidden sm:flex\">\n                <Plus className=\"mr-2 h-4 w-4\" />\n                New Project\n              </Button>\n\n              <Button variant=\"ghost\" size=\"sm\">\n                <Bell className=\"h-5 w-5\" />\n              </Button>\n\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                    <Avatar className=\"h-8 w-8\">\n                      <AvatarImage src={session?.user?.image || ''} alt={session?.user?.name || ''} />\n                      <AvatarFallback>\n                        {session?.user?.name?.charAt(0) || session?.user?.email?.charAt(0) || 'U'}\n                      </AvatarFallback>\n                    </Avatar>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                  <DropdownMenuLabel className=\"font-normal\">\n                    <div className=\"flex flex-col space-y-1\">\n                      <p className=\"text-sm font-medium leading-none\">{session?.user?.name}</p>\n                      <p className=\"text-xs leading-none text-muted-foreground\">\n                        {session?.user?.email}\n                      </p>\n                    </div>\n                  </DropdownMenuLabel>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard/profile\">Profile</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard/settings\">Settings</Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem\n                    className=\"text-red-600\"\n                    onClick={() => signOut({ callbackUrl: '/auth/signin' })}\n                  >\n                    <LogOut className=\"mr-2 h-4 w-4\" />\n                    Sign out\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main\n          className=\"py-6\"\n          id=\"main-content\"\n          role=\"main\"\n          aria-label=\"Main content\"\n        >\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAnBA;;;;;;;;;;;;;AAoCA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,mMAAA,CAAA,OAAI;IAAC;IACpD;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,kNAAA,CAAA,aAAU;IAAC;IAClE;QAAE,MAAM;QAAe,MAAM;QAA0B,MAAM,8MAAA,CAAA,WAAQ;IAAC;IACtE;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,oMAAA,CAAA,QAAK;IAAC;IACrD;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACjE;AAEM,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,+IAAA,CAAA,YAAS;;;;;0BAGV,8OAAC,iIAAA,CAAA,QAAK;gBAAC,MAAM;gBAAa,cAAc;0BACtC,cAAA,8OAAC,iIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAO,WAAU;8BAClC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;;sDAChC,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;sDAClD,8OAAC,uJAAA,CAAA,mBAAgB;sDAAC;;;;;;;;;;;;;;;;;0CAGtB,8OAAC;gCACC,WAAU;gCACV,IAAG;gCACH,MAAK;gCACL,cAAW;0CAEV,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,8BACA,mCACJ;wCACF,SAAS,IAAM,eAAe;wCAC9B,gBAAc,WAAW,SAAS;;0DAElC,8OAAC,KAAK,IAAI;gDAAC,WAAU;gDAAe,eAAY;;;;;;4CAC/C,KAAK,IAAI;;uCAXL,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;;;;;;;;;;;;0BAOR,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAChC,cAAA,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;sCAGtD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,8BACA,mCACJ;;sDAEF,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCATL,KAAK,IAAI;;;;;4BAYpB;;;;;;;;;;;;;;;;;0BAMN,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;8CAC1C,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAIpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDACC,WAAU;gDACV,aAAY;gDACZ,MAAK;;;;;;;;;;;;kDAGT,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,WAAU;;kEAC1B,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAInC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;0DAC3B,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAGlB,8OAAC,4IAAA,CAAA,eAAY;;kEACX,8OAAC,4IAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,WAAU;sEAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;;kFAChB,8OAAC,kIAAA,CAAA,cAAW;wEAAC,KAAK,SAAS,MAAM,SAAS;wEAAI,KAAK,SAAS,MAAM,QAAQ;;;;;;kFAC1E,8OAAC,kIAAA,CAAA,iBAAc;kFACZ,SAAS,MAAM,MAAM,OAAO,MAAM,SAAS,MAAM,OAAO,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;kEAK9E,8OAAC,4IAAA,CAAA,sBAAmB;wDAAC,WAAU;wDAAO,OAAM;wDAAM,UAAU;;0EAC1D,8OAAC,4IAAA,CAAA,oBAAiB;gEAAC,WAAU;0EAC3B,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAoC,SAAS,MAAM;;;;;;sFAChE,8OAAC;4EAAE,WAAU;sFACV,SAAS,MAAM;;;;;;;;;;;;;;;;;0EAItB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0EACtB,8OAAC,4IAAA,CAAA,mBAAgB;gEAAC,OAAO;0EACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;8EAAqB;;;;;;;;;;;0EAElC,8OAAC,4IAAA,CAAA,mBAAgB;gEAAC,OAAO;0EACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAK;8EAAsB;;;;;;;;;;;0EAEnC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0EACtB,8OAAC,4IAAA,CAAA,mBAAgB;gEACf,WAAU;gEACV,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;wEAAE,aAAa;oEAAe;;kFAErD,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU/C,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,MAAK;wBACL,cAAW;kCAEX,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1244, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1341, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1420, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"relative w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1530, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/common/DataTable.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { \n  ChevronLeft, \n  ChevronRight, \n  Search, \n  Filter,\n  MoreHorizontal,\n  ArrowUpDown\n} from 'lucide-react';\n\nexport interface Column<T> {\n  key: keyof T;\n  label: string;\n  sortable?: boolean;\n  filterable?: boolean;\n  render?: (value: any, row: T) => React.ReactNode;\n  width?: string;\n}\n\nexport interface DataTableProps<T> {\n  data: T[];\n  columns: Column<T>[];\n  searchable?: boolean;\n  searchPlaceholder?: string;\n  filterable?: boolean;\n  sortable?: boolean;\n  pagination?: boolean;\n  pageSize?: number;\n  actions?: (row: T) => React.ReactNode;\n  onRowClick?: (row: T) => void;\n  loading?: boolean;\n  emptyMessage?: string;\n  className?: string;\n}\n\nexport function DataTable<T extends Record<string, any>>({\n  data,\n  columns,\n  searchable = true,\n  searchPlaceholder = 'Search...',\n  filterable = false,\n  sortable = true,\n  pagination = true,\n  pageSize = 10,\n  actions,\n  onRowClick,\n  loading = false,\n  emptyMessage = 'No data available',\n  className = '',\n}: DataTableProps<T>) {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [sortColumn, setSortColumn] = useState<keyof T | null>(null);\n  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');\n  const [currentPage, setCurrentPage] = useState(1);\n\n  // Filter data based on search query\n  const filteredData = searchable\n    ? data.filter((row) =>\n        columns.some((column) => {\n          const value = row[column.key];\n          return value?.toString().toLowerCase().includes(searchQuery.toLowerCase());\n        })\n      )\n    : data;\n\n  // Sort data\n  const sortedData = sortable && sortColumn\n    ? [...filteredData].sort((a, b) => {\n        const aValue = a[sortColumn];\n        const bValue = b[sortColumn];\n        \n        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;\n        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;\n        return 0;\n      })\n    : filteredData;\n\n  // Paginate data\n  const totalPages = Math.ceil(sortedData.length / pageSize);\n  const paginatedData = pagination\n    ? sortedData.slice((currentPage - 1) * pageSize, currentPage * pageSize)\n    : sortedData;\n\n  const handleSort = (column: keyof T) => {\n    if (sortColumn === column) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortColumn(column);\n      setSortDirection('asc');\n    }\n  };\n\n  const handlePageChange = (page: number) => {\n    setCurrentPage(Math.max(1, Math.min(page, totalPages)));\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* Search and Filters */}\n      {(searchable || filterable) && (\n        <div className=\"flex items-center justify-between\">\n          {searchable && (\n            <div className=\"relative max-w-sm\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n              <Input\n                placeholder={searchPlaceholder}\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n          )}\n          {filterable && (\n            <Button variant=\"outline\" size=\"sm\">\n              <Filter className=\"mr-2 h-4 w-4\" />\n              Filters\n            </Button>\n          )}\n        </div>\n      )}\n\n      {/* Table */}\n      <div className=\"rounded-md border\">\n        <Table>\n          <TableHeader>\n            <TableRow>\n              {columns.map((column) => (\n                <TableHead\n                  key={String(column.key)}\n                  style={{ width: column.width }}\n                  className={sortable && column.sortable !== false ? 'cursor-pointer select-none' : ''}\n                  onClick={() => sortable && column.sortable !== false && handleSort(column.key)}\n                >\n                  <div className=\"flex items-center space-x-2\">\n                    <span>{column.label}</span>\n                    {sortable && column.sortable !== false && (\n                      <ArrowUpDown className=\"h-4 w-4\" />\n                    )}\n                  </div>\n                </TableHead>\n              ))}\n              {actions && <TableHead className=\"w-[100px]\">Actions</TableHead>}\n            </TableRow>\n          </TableHeader>\n          <TableBody>\n            {paginatedData.length === 0 ? (\n              <TableRow>\n                <TableCell\n                  colSpan={columns.length + (actions ? 1 : 0)}\n                  className=\"h-24 text-center\"\n                >\n                  {emptyMessage}\n                </TableCell>\n              </TableRow>\n            ) : (\n              paginatedData.map((row, index) => (\n                <TableRow\n                  key={index}\n                  className={onRowClick ? 'cursor-pointer' : ''}\n                  onClick={() => onRowClick?.(row)}\n                >\n                  {columns.map((column) => (\n                    <TableCell key={String(column.key)}>\n                      {column.render\n                        ? column.render(row[column.key], row)\n                        : row[column.key]}\n                    </TableCell>\n                  ))}\n                  {actions && (\n                    <TableCell>\n                      <DropdownMenu>\n                        <DropdownMenuTrigger asChild>\n                          <Button variant=\"ghost\" size=\"sm\">\n                            <MoreHorizontal className=\"h-4 w-4\" />\n                          </Button>\n                        </DropdownMenuTrigger>\n                        <DropdownMenuContent align=\"end\">\n                          {actions(row)}\n                        </DropdownMenuContent>\n                      </DropdownMenu>\n                    </TableCell>\n                  )}\n                </TableRow>\n              ))\n            )}\n          </TableBody>\n        </Table>\n      </div>\n\n      {/* Pagination */}\n      {pagination && totalPages > 1 && (\n        <div className=\"flex items-center justify-between\">\n          <div className=\"text-sm text-gray-700\">\n            Showing {(currentPage - 1) * pageSize + 1} to{' '}\n            {Math.min(currentPage * pageSize, sortedData.length)} of {sortedData.length} results\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => handlePageChange(currentPage - 1)}\n              disabled={currentPage === 1}\n            >\n              <ChevronLeft className=\"h-4 w-4\" />\n              Previous\n            </Button>\n            <div className=\"flex items-center space-x-1\">\n              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                const page = i + 1;\n                return (\n                  <Button\n                    key={page}\n                    variant={currentPage === page ? 'default' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => handlePageChange(page)}\n                  >\n                    {page}\n                  </Button>\n                );\n              })}\n            </div>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => handlePageChange(currentPage + 1)}\n              disabled={currentPage === totalPages}\n            >\n              Next\n              <ChevronRight className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAEA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AApBA;;;;;;;;AAsDO,SAAS,UAAyC,EACvD,IAAI,EACJ,OAAO,EACP,aAAa,IAAI,EACjB,oBAAoB,WAAW,EAC/B,aAAa,KAAK,EAClB,WAAW,IAAI,EACf,aAAa,IAAI,EACjB,WAAW,EAAE,EACb,OAAO,EACP,UAAU,EACV,UAAU,KAAK,EACf,eAAe,mBAAmB,EAClC,YAAY,EAAE,EACI;IAClB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,oCAAoC;IACpC,MAAM,eAAe,aACjB,KAAK,MAAM,CAAC,CAAC,MACX,QAAQ,IAAI,CAAC,CAAC;YACZ,MAAM,QAAQ,GAAG,CAAC,OAAO,GAAG,CAAC;YAC7B,OAAO,OAAO,WAAW,cAAc,SAAS,YAAY,WAAW;QACzE,MAEF;IAEJ,YAAY;IACZ,MAAM,aAAa,YAAY,aAC3B;WAAI;KAAa,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,SAAS,CAAC,CAAC,WAAW;QAC5B,MAAM,SAAS,CAAC,CAAC,WAAW;QAE5B,IAAI,SAAS,QAAQ,OAAO,kBAAkB,QAAQ,CAAC,IAAI;QAC3D,IAAI,SAAS,QAAQ,OAAO,kBAAkB,QAAQ,IAAI,CAAC;QAC3D,OAAO;IACT,KACA;IAEJ,gBAAgB;IAChB,MAAM,aAAa,KAAK,IAAI,CAAC,WAAW,MAAM,GAAG;IACjD,MAAM,gBAAgB,aAClB,WAAW,KAAK,CAAC,CAAC,cAAc,CAAC,IAAI,UAAU,cAAc,YAC7D;IAEJ,MAAM,aAAa,CAAC;QAClB,IAAI,eAAe,QAAQ;YACzB,iBAAiB,kBAAkB,QAAQ,SAAS;QACtD,OAAO;YACL,cAAc;YACd,iBAAiB;QACnB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM;IAC5C;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;YAErC,CAAC,cAAc,UAAU,mBACxB,8OAAC;gBAAI,WAAU;;oBACZ,4BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,aAAa;gCACb,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;oBAIf,4BACC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;;0CAC7B,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAQ3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;sCACJ,8OAAC,iIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;oCACN,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;4CAER,OAAO;gDAAE,OAAO,OAAO,KAAK;4CAAC;4CAC7B,WAAW,YAAY,OAAO,QAAQ,KAAK,QAAQ,+BAA+B;4CAClF,SAAS,IAAM,YAAY,OAAO,QAAQ,KAAK,SAAS,WAAW,OAAO,GAAG;sDAE7E,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAM,OAAO,KAAK;;;;;;oDAClB,YAAY,OAAO,QAAQ,KAAK,uBAC/B,8OAAC,wNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;2CARtB,OAAO,OAAO,GAAG;;;;;oCAazB,yBAAW,8OAAC,iIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAY;;;;;;;;;;;;;;;;;sCAGjD,8OAAC,iIAAA,CAAA,YAAS;sCACP,cAAc,MAAM,KAAK,kBACxB,8OAAC,iIAAA,CAAA,WAAQ;0CACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;oCACR,SAAS,QAAQ,MAAM,GAAG,CAAC,UAAU,IAAI,CAAC;oCAC1C,WAAU;8CAET;;;;;;;;;;uCAIL,cAAc,GAAG,CAAC,CAAC,KAAK,sBACtB,8OAAC,iIAAA,CAAA,WAAQ;oCAEP,WAAW,aAAa,mBAAmB;oCAC3C,SAAS,IAAM,aAAa;;wCAE3B,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;0DACP,OAAO,MAAM,GACV,OAAO,MAAM,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,EAAE,OAC/B,GAAG,CAAC,OAAO,GAAG,CAAC;+CAHL,OAAO,OAAO,GAAG;;;;;wCAMlC,yBACC,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC,4IAAA,CAAA,eAAY;;kEACX,8OAAC,4IAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;sEAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wDAAC,OAAM;kEACxB,QAAQ;;;;;;;;;;;;;;;;;;mCApBZ;;;;;;;;;;;;;;;;;;;;;YAiChB,cAAc,aAAa,mBAC1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BAAwB;4BAC5B,CAAC,cAAc,CAAC,IAAI,WAAW;4BAAE;4BAAI;4BAC7C,KAAK,GAAG,CAAC,cAAc,UAAU,WAAW,MAAM;4BAAE;4BAAK,WAAW,MAAM;4BAAC;;;;;;;kCAE9E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,iBAAiB,cAAc;gCAC9C,UAAU,gBAAgB;;kDAE1B,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ,KAAK,GAAG,CAAC,GAAG;gCAAY,GAAG,CAAC,GAAG;oCACnD,MAAM,OAAO,IAAI;oCACjB,qBACE,8OAAC,kIAAA,CAAA,SAAM;wCAEL,SAAS,gBAAgB,OAAO,YAAY;wCAC5C,MAAK;wCACL,SAAS,IAAM,iBAAiB;kDAE/B;uCALI;;;;;gCAQX;;;;;;0CAEF,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,iBAAiB,cAAc;gCAC9C,UAAU,gBAAgB;;oCAC3B;kDAEC,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC", "debugId": null}}, {"offset": {"line": 1921, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1967, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/common/StatusBadge.tsx"], "sourcesContent": ["import { Badge } from '@/components/ui/badge';\nimport { cn } from '@/lib/utils';\nimport { \n  CheckCircle, \n  Clock, \n  AlertCircle, \n  XCircle,\n  Pause,\n  Play\n} from 'lucide-react';\n\nexport type StatusType = \n  | 'success' \n  | 'pending' \n  | 'warning' \n  | 'error' \n  | 'info' \n  | 'active' \n  | 'inactive'\n  | 'draft'\n  | 'in_progress'\n  | 'review'\n  | 'completed'\n  | 'on_hold'\n  | 'approved'\n  | 'rejected';\n\ninterface StatusBadgeProps {\n  status: StatusType;\n  text?: string;\n  showIcon?: boolean;\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nconst statusConfig = {\n  success: {\n    color: 'bg-green-100 text-green-800 border-green-200',\n    icon: CheckCircle,\n    defaultText: 'Success',\n  },\n  pending: {\n    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',\n    icon: Clock,\n    defaultText: 'Pending',\n  },\n  warning: {\n    color: 'bg-orange-100 text-orange-800 border-orange-200',\n    icon: AlertCircle,\n    defaultText: 'Warning',\n  },\n  error: {\n    color: 'bg-red-100 text-red-800 border-red-200',\n    icon: XCircle,\n    defaultText: 'Error',\n  },\n  info: {\n    color: 'bg-blue-100 text-blue-800 border-blue-200',\n    icon: AlertCircle,\n    defaultText: 'Info',\n  },\n  active: {\n    color: 'bg-green-100 text-green-800 border-green-200',\n    icon: Play,\n    defaultText: 'Active',\n  },\n  inactive: {\n    color: 'bg-gray-100 text-gray-800 border-gray-200',\n    icon: Pause,\n    defaultText: 'Inactive',\n  },\n  draft: {\n    color: 'bg-gray-100 text-gray-800 border-gray-200',\n    icon: Clock,\n    defaultText: 'Draft',\n  },\n  in_progress: {\n    color: 'bg-blue-100 text-blue-800 border-blue-200',\n    icon: Play,\n    defaultText: 'In Progress',\n  },\n  review: {\n    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',\n    icon: AlertCircle,\n    defaultText: 'In Review',\n  },\n  completed: {\n    color: 'bg-green-100 text-green-800 border-green-200',\n    icon: CheckCircle,\n    defaultText: 'Completed',\n  },\n  on_hold: {\n    color: 'bg-red-100 text-red-800 border-red-200',\n    icon: Pause,\n    defaultText: 'On Hold',\n  },\n  approved: {\n    color: 'bg-green-100 text-green-800 border-green-200',\n    icon: CheckCircle,\n    defaultText: 'Approved',\n  },\n  rejected: {\n    color: 'bg-red-100 text-red-800 border-red-200',\n    icon: XCircle,\n    defaultText: 'Rejected',\n  },\n};\n\nconst sizeClasses = {\n  sm: 'text-xs px-2 py-1',\n  md: 'text-sm px-2.5 py-1.5',\n  lg: 'text-base px-3 py-2',\n};\n\nconst iconSizes = {\n  sm: 'h-3 w-3',\n  md: 'h-4 w-4',\n  lg: 'h-5 w-5',\n};\n\nexport function StatusBadge({\n  status,\n  text,\n  showIcon = true,\n  size = 'md',\n  className = '',\n}: StatusBadgeProps) {\n  const config = statusConfig[status];\n  const Icon = config.icon;\n  const displayText = text || config.defaultText;\n\n  return (\n    <Badge\n      variant=\"outline\"\n      className={cn(\n        config.color,\n        sizeClasses[size],\n        'inline-flex items-center gap-1.5 font-medium border',\n        className\n      )}\n    >\n      {showIcon && (\n        <Icon className={iconSizes[size]} />\n      )}\n      {displayText}\n    </Badge>\n  );\n}\n\n// Convenience components for common statuses\nexport function ProjectStatusBadge({ \n  status, \n  ...props \n}: Omit<StatusBadgeProps, 'status'> & { \n  status: 'draft' | 'in_progress' | 'review' | 'completed' | 'on_hold' \n}) {\n  return <StatusBadge status={status} {...props} />;\n}\n\nexport function UserStatusBadge({ \n  status, \n  ...props \n}: Omit<StatusBadgeProps, 'status'> & { \n  status: 'active' | 'inactive' | 'pending' \n}) {\n  return <StatusBadge status={status} {...props} />;\n}\n\nexport function ApprovalStatusBadge({ \n  status, \n  ...props \n}: Omit<StatusBadgeProps, 'status'> & { \n  status: 'pending' | 'approved' | 'rejected' \n}) {\n  return <StatusBadge status={status} {...props} />;\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAiCA,MAAM,eAAe;IACnB,SAAS;QACP,OAAO;QACP,MAAM,2NAAA,CAAA,cAAW;QACjB,aAAa;IACf;IACA,SAAS;QACP,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;QACX,aAAa;IACf;IACA,SAAS;QACP,OAAO;QACP,MAAM,oNAAA,CAAA,cAAW;QACjB,aAAa;IACf;IACA,OAAO;QACL,OAAO;QACP,MAAM,4MAAA,CAAA,UAAO;QACb,aAAa;IACf;IACA,MAAM;QACJ,OAAO;QACP,MAAM,oNAAA,CAAA,cAAW;QACjB,aAAa;IACf;IACA,QAAQ;QACN,OAAO;QACP,MAAM,kMAAA,CAAA,OAAI;QACV,aAAa;IACf;IACA,UAAU;QACR,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;QACX,aAAa;IACf;IACA,OAAO;QACL,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;QACX,aAAa;IACf;IACA,aAAa;QACX,OAAO;QACP,MAAM,kMAAA,CAAA,OAAI;QACV,aAAa;IACf;IACA,QAAQ;QACN,OAAO;QACP,MAAM,oNAAA,CAAA,cAAW;QACjB,aAAa;IACf;IACA,WAAW;QACT,OAAO;QACP,MAAM,2NAAA,CAAA,cAAW;QACjB,aAAa;IACf;IACA,SAAS;QACP,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;QACX,aAAa;IACf;IACA,UAAU;QACR,OAAO;QACP,MAAM,2NAAA,CAAA,cAAW;QACjB,aAAa;IACf;IACA,UAAU;QACR,OAAO;QACP,MAAM,4MAAA,CAAA,UAAO;QACb,aAAa;IACf;AACF;AAEA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEA,MAAM,YAAY;IAChB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,YAAY,EAC1B,MAAM,EACN,IAAI,EACJ,WAAW,IAAI,EACf,OAAO,IAAI,EACX,YAAY,EAAE,EACG;IACjB,MAAM,SAAS,YAAY,CAAC,OAAO;IACnC,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,cAAc,QAAQ,OAAO,WAAW;IAE9C,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,SAAQ;QACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OAAO,KAAK,EACZ,WAAW,CAAC,KAAK,EACjB,uDACA;;YAGD,0BACC,8OAAC;gBAAK,WAAW,SAAS,CAAC,KAAK;;;;;;YAEjC;;;;;;;AAGP;AAGO,SAAS,mBAAmB,EACjC,MAAM,EACN,GAAG,OAGJ;IACC,qBAAO,8OAAC;QAAY,QAAQ;QAAS,GAAG,KAAK;;;;;;AAC/C;AAEO,SAAS,gBAAgB,EAC9B,MAAM,EACN,GAAG,OAGJ;IACC,qBAAO,8OAAC;QAAY,QAAQ;QAAS,GAAG,KAAK;;;;;;AAC/C;AAEO,SAAS,oBAAoB,EAClC,MAAM,EACN,GAAG,OAGJ;IACC,qBAAO,8OAAC;QAAY,QAAQ;QAAS,GAAG,KAAK;;;;;;AAC/C", "debugId": null}}, {"offset": {"line": 2127, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/seo/SEOHead.tsx"], "sourcesContent": ["import Head from 'next/head';\n\ninterface SEOHeadProps {\n  title?: string;\n  description?: string;\n  keywords?: string[];\n  canonicalUrl?: string;\n  ogImage?: string;\n  ogType?: 'website' | 'article' | 'product';\n  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';\n  noIndex?: boolean;\n  noFollow?: boolean;\n  structuredData?: object;\n}\n\nconst defaultMeta = {\n  title: 'GlossaFlow - Professional Translation Management Platform',\n  description: 'Streamline your translation workflow with GlossaFlow. Manage projects, terminology, and collaborate with translators efficiently.',\n  keywords: [\n    'translation management',\n    'localization',\n    'terminology management',\n    'translation workflow',\n    'CAT tool',\n    'translation memory',\n    'project management',\n    'multilingual',\n  ],\n  ogImage: '/images/og-image.png',\n  ogType: 'website' as const,\n  twitterCard: 'summary_large_image' as const,\n};\n\nexport function SEOHead({\n  title,\n  description = defaultMeta.description,\n  keywords = defaultMeta.keywords,\n  canonicalUrl,\n  ogImage = defaultMeta.ogImage,\n  ogType = defaultMeta.ogType,\n  twitterCard = defaultMeta.twitterCard,\n  noIndex = false,\n  noFollow = false,\n  structuredData,\n}: SEOHeadProps) {\n  const fullTitle = title \n    ? `${title} | GlossaFlow`\n    : defaultMeta.title;\n\n  const robotsContent = [\n    noIndex ? 'noindex' : 'index',\n    noFollow ? 'nofollow' : 'follow',\n  ].join(', ');\n\n  return (\n    <Head>\n      {/* Basic Meta Tags */}\n      <title>{fullTitle}</title>\n      <meta name=\"description\" content={description} />\n      <meta name=\"keywords\" content={keywords.join(', ')} />\n      <meta name=\"robots\" content={robotsContent} />\n      \n      {/* Canonical URL */}\n      {canonicalUrl && <link rel=\"canonical\" href={canonicalUrl} />}\n      \n      {/* Open Graph Meta Tags */}\n      <meta property=\"og:title\" content={fullTitle} />\n      <meta property=\"og:description\" content={description} />\n      <meta property=\"og:type\" content={ogType} />\n      <meta property=\"og:image\" content={ogImage} />\n      <meta property=\"og:site_name\" content=\"GlossaFlow\" />\n      {canonicalUrl && <meta property=\"og:url\" content={canonicalUrl} />}\n      \n      {/* Twitter Card Meta Tags */}\n      <meta name=\"twitter:card\" content={twitterCard} />\n      <meta name=\"twitter:title\" content={fullTitle} />\n      <meta name=\"twitter:description\" content={description} />\n      <meta name=\"twitter:image\" content={ogImage} />\n      \n      {/* Additional Meta Tags */}\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n      <meta name=\"theme-color\" content=\"#2563eb\" />\n      <meta name=\"msapplication-TileColor\" content=\"#2563eb\" />\n      \n      {/* Favicon */}\n      <link rel=\"icon\" href=\"/favicon.ico\" />\n      <link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/favicon-32x32.png\" />\n      <link rel=\"icon\" type=\"image/png\" sizes=\"16x16\" href=\"/favicon-16x16.png\" />\n      <link rel=\"apple-touch-icon\" sizes=\"180x180\" href=\"/apple-touch-icon.png\" />\n      <link rel=\"manifest\" href=\"/site.webmanifest\" />\n      \n      {/* Structured Data */}\n      {structuredData && (\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{\n            __html: JSON.stringify(structuredData),\n          }}\n        />\n      )}\n    </Head>\n  );\n}\n\n// Predefined structured data schemas\nexport const createOrganizationSchema = (organization: {\n  name: string;\n  url: string;\n  logo?: string;\n  description?: string;\n}) => ({\n  '@context': 'https://schema.org',\n  '@type': 'Organization',\n  name: organization.name,\n  url: organization.url,\n  logo: organization.logo,\n  description: organization.description,\n});\n\nexport const createSoftwareApplicationSchema = () => ({\n  '@context': 'https://schema.org',\n  '@type': 'SoftwareApplication',\n  name: 'GlossaFlow',\n  applicationCategory: 'BusinessApplication',\n  operatingSystem: 'Web Browser',\n  description: 'Professional translation management platform for streamlined localization workflows',\n  offers: {\n    '@type': 'Offer',\n    price: '0',\n    priceCurrency: 'USD',\n    description: 'Free tier available',\n  },\n  featureList: [\n    'Translation Project Management',\n    'Terminology Management',\n    'Translation Memory',\n    'Collaborative Translation',\n    'Quality Assurance',\n    'Workflow Automation',\n  ],\n});\n\nexport const createBreadcrumbSchema = (breadcrumbs: Array<{ name: string; url: string }>) => ({\n  '@context': 'https://schema.org',\n  '@type': 'BreadcrumbList',\n  itemListElement: breadcrumbs.map((breadcrumb, index) => ({\n    '@type': 'ListItem',\n    position: index + 1,\n    name: breadcrumb.name,\n    item: breadcrumb.url,\n  })),\n});\n"], "names": [], "mappings": ";;;;;;;AAAA;;;AAeA,MAAM,cAAc;IAClB,OAAO;IACP,aAAa;IACb,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,SAAS;IACT,QAAQ;IACR,aAAa;AACf;AAEO,SAAS,QAAQ,EACtB,KAAK,EACL,cAAc,YAAY,WAAW,EACrC,WAAW,YAAY,QAAQ,EAC/B,YAAY,EACZ,UAAU,YAAY,OAAO,EAC7B,SAAS,YAAY,MAAM,EAC3B,cAAc,YAAY,WAAW,EACrC,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,cAAc,EACD;IACb,MAAM,YAAY,QACd,GAAG,MAAM,aAAa,CAAC,GACvB,YAAY,KAAK;IAErB,MAAM,gBAAgB;QACpB,UAAU,YAAY;QACtB,WAAW,aAAa;KACzB,CAAC,IAAI,CAAC;IAEP,qBACE,8OAAC,oKAAA,CAAA,UAAI;;0BAEH,8OAAC;0BAAO;;;;;;0BACR,8OAAC;gBAAK,MAAK;gBAAc,SAAS;;;;;;0BAClC,8OAAC;gBAAK,MAAK;gBAAW,SAAS,SAAS,IAAI,CAAC;;;;;;0BAC7C,8OAAC;gBAAK,MAAK;gBAAS,SAAS;;;;;;YAG5B,8BAAgB,8OAAC;gBAAK,KAAI;gBAAY,MAAM;;;;;;0BAG7C,8OAAC;gBAAK,UAAS;gBAAW,SAAS;;;;;;0BACnC,8OAAC;gBAAK,UAAS;gBAAiB,SAAS;;;;;;0BACzC,8OAAC;gBAAK,UAAS;gBAAU,SAAS;;;;;;0BAClC,8OAAC;gBAAK,UAAS;gBAAW,SAAS;;;;;;0BACnC,8OAAC;gBAAK,UAAS;gBAAe,SAAQ;;;;;;YACrC,8BAAgB,8OAAC;gBAAK,UAAS;gBAAS,SAAS;;;;;;0BAGlD,8OAAC;gBAAK,MAAK;gBAAe,SAAS;;;;;;0BACnC,8OAAC;gBAAK,MAAK;gBAAgB,SAAS;;;;;;0BACpC,8OAAC;gBAAK,MAAK;gBAAsB,SAAS;;;;;;0BAC1C,8OAAC;gBAAK,MAAK;gBAAgB,SAAS;;;;;;0BAGpC,8OAAC;gBAAK,MAAK;gBAAW,SAAQ;;;;;;0BAC9B,8OAAC;gBAAK,MAAK;gBAAc,SAAQ;;;;;;0BACjC,8OAAC;gBAAK,MAAK;gBAA0B,SAAQ;;;;;;0BAG7C,8OAAC;gBAAK,KAAI;gBAAO,MAAK;;;;;;0BACtB,8OAAC;gBAAK,KAAI;gBAAO,MAAK;gBAAY,OAAM;gBAAQ,MAAK;;;;;;0BACrD,8OAAC;gBAAK,KAAI;gBAAO,MAAK;gBAAY,OAAM;gBAAQ,MAAK;;;;;;0BACrD,8OAAC;gBAAK,KAAI;gBAAmB,OAAM;gBAAU,MAAK;;;;;;0BAClD,8OAAC;gBAAK,KAAI;gBAAW,MAAK;;;;;;YAGzB,gCACC,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC;gBACzB;;;;;;;;;;;;AAKV;AAGO,MAAM,2BAA2B,CAAC,eAKnC,CAAC;QACL,YAAY;QACZ,SAAS;QACT,MAAM,aAAa,IAAI;QACvB,KAAK,aAAa,GAAG;QACrB,MAAM,aAAa,IAAI;QACvB,aAAa,aAAa,WAAW;IACvC,CAAC;AAEM,MAAM,kCAAkC,IAAM,CAAC;QACpD,YAAY;QACZ,SAAS;QACT,MAAM;QACN,qBAAqB;QACrB,iBAAiB;QACjB,aAAa;QACb,QAAQ;YACN,SAAS;YACT,OAAO;YACP,eAAe;YACf,aAAa;QACf;QACA,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;SACD;IACH,CAAC;AAEM,MAAM,yBAAyB,CAAC,cAAsD,CAAC;QAC5F,YAAY;QACZ,SAAS;QACT,iBAAiB,YAAY,GAAG,CAAC,CAAC,YAAY,QAAU,CAAC;gBACvD,SAAS;gBACT,UAAU,QAAQ;gBAClB,MAAM,WAAW,IAAI;gBACrB,MAAM,WAAW,GAAG;YACtB,CAAC;IACH,CAAC", "debugId": null}}, {"offset": {"line": 2413, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/app/dashboard/translation/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { DashboardLayout } from '@/components/layout/DashboardLayout';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Badge } from '@/components/ui/badge';\nimport { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { DataTable } from '@/components/common/DataTable';\nimport { StatusBadge } from '@/components/common/StatusBadge';\nimport { SEOHead } from '@/components/seo/SEOHead';\nimport {\n  Search,\n  Filter,\n  FileText,\n  Clock,\n  CheckCircle,\n  AlertCircle,\n  Play,\n  MoreHorizontal,\n  Eye,\n  Edit,\n  MessageSquare,\n} from 'lucide-react';\n\n// Mock data for translation segments\nconst mockSegments = [\n  {\n    id: '1',\n    projectName: 'Website Localization',\n    fileName: 'homepage.json',\n    segmentNumber: 1,\n    sourceText: 'Welcome to our platform',\n    targetText: 'Bienvenido a nuestra plataforma',\n    status: 'completed',\n    wordCount: 4,\n    assignedTo: '<PERSON>',\n    deadline: '2024-02-15',\n    progress: 100,\n  },\n  {\n    id: '2',\n    projectName: 'Mobile App',\n    fileName: 'navigation.json',\n    segmentNumber: 5,\n    sourceText: 'Settings and preferences',\n    targetText: '',\n    status: 'in_progress',\n    wordCount: 3,\n    assignedTo: 'Mike Johnson',\n    deadline: '2024-02-20',\n    progress: 0,\n  },\n  {\n    id: '3',\n    projectName: 'Documentation',\n    fileName: 'user-guide.md',\n    segmentNumber: 12,\n    sourceText: 'Click the button to continue',\n    targetText: 'Haga clic en el botón para continuar',\n    status: 'review',\n    wordCount: 6,\n    assignedTo: 'Sarah Chen',\n    deadline: '2024-02-18',\n    progress: 100,\n  },\n];\n\nconst columns = [\n  {\n    key: 'projectName' as const,\n    label: 'Project',\n    sortable: true,\n  },\n  {\n    key: 'fileName' as const,\n    label: 'File',\n    sortable: true,\n  },\n  {\n    key: 'segmentNumber' as const,\n    label: 'Segment',\n    sortable: true,\n  },\n  {\n    key: 'sourceText' as const,\n    label: 'Source Text',\n    render: (value: string) => (\n      <div className=\"max-w-xs truncate\" title={value}>\n        {value}\n      </div>\n    ),\n  },\n  {\n    key: 'status' as const,\n    label: 'Status',\n    render: (value: string) => (\n      <StatusBadge status={value as any} />\n    ),\n  },\n  {\n    key: 'wordCount' as const,\n    label: 'Words',\n    sortable: true,\n  },\n  {\n    key: 'assignedTo' as const,\n    label: 'Assigned To',\n    sortable: true,\n  },\n  {\n    key: 'deadline' as const,\n    label: 'Deadline',\n    sortable: true,\n    render: (value: string) => new Date(value).toLocaleDateString(),\n  },\n];\n\nexport default function TranslationPage() {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n\n  const filteredSegments = mockSegments.filter(segment => {\n    const matchesSearch = segment.sourceText.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         segment.projectName.toLowerCase().includes(searchQuery.toLowerCase());\n    const matchesStatus = selectedStatus === 'all' || segment.status === selectedStatus;\n    return matchesSearch && matchesStatus;\n  });\n\n  const stats = {\n    total: mockSegments.length,\n    pending: mockSegments.filter(s => s.status === 'pending').length,\n    inProgress: mockSegments.filter(s => s.status === 'in_progress').length,\n    completed: mockSegments.filter(s => s.status === 'completed').length,\n    review: mockSegments.filter(s => s.status === 'review').length,\n  };\n\n  return (\n    <>\n      <SEOHead\n        title=\"Translation Workspace\"\n        description=\"Manage your translation assignments and work on translation segments\"\n        noIndex={true}\n      />\n      \n      <DashboardLayout>\n        <div className=\"space-y-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Translation Workspace</h1>\n              <p className=\"text-gray-600\">Manage your translation assignments and segments</p>\n            </div>\n            <Button>\n              <Play className=\"mr-2 h-4 w-4\" />\n              Start Translating\n            </Button>\n          </div>\n\n          {/* Stats Cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n            <Card>\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm text-gray-600\">Total Segments</p>\n                    <p className=\"text-2xl font-bold\">{stats.total}</p>\n                  </div>\n                  <FileText className=\"h-8 w-8 text-gray-400\" />\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm text-gray-600\">In Progress</p>\n                    <p className=\"text-2xl font-bold text-blue-600\">{stats.inProgress}</p>\n                  </div>\n                  <Clock className=\"h-8 w-8 text-blue-400\" />\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm text-gray-600\">Completed</p>\n                    <p className=\"text-2xl font-bold text-green-600\">{stats.completed}</p>\n                  </div>\n                  <CheckCircle className=\"h-8 w-8 text-green-400\" />\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm text-gray-600\">In Review</p>\n                    <p className=\"text-2xl font-bold text-yellow-600\">{stats.review}</p>\n                  </div>\n                  <AlertCircle className=\"h-8 w-8 text-yellow-400\" />\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm text-gray-600\">Pending</p>\n                    <p className=\"text-2xl font-bold text-gray-600\">{stats.pending}</p>\n                  </div>\n                  <Clock className=\"h-8 w-8 text-gray-400\" />\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          <Tabs defaultValue=\"my-assignments\" className=\"space-y-4\">\n            <TabsList>\n              <TabsTrigger value=\"my-assignments\">My Assignments</TabsTrigger>\n              <TabsTrigger value=\"available\">Available Work</TabsTrigger>\n              <TabsTrigger value=\"completed\">Completed</TabsTrigger>\n            </TabsList>\n\n            <TabsContent value=\"my-assignments\" className=\"space-y-4\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>My Translation Assignments</CardTitle>\n                  <CardDescription>\n                    Segments assigned to you for translation\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"flex items-center space-x-4 mb-4\">\n                    <div className=\"relative flex-1 max-w-sm\">\n                      <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                      <Input\n                        placeholder=\"Search segments...\"\n                        value={searchQuery}\n                        onChange={(e) => setSearchQuery(e.target.value)}\n                        className=\"pl-10\"\n                      />\n                    </div>\n                    <select\n                      value={selectedStatus}\n                      onChange={(e) => setSelectedStatus(e.target.value)}\n                      className=\"px-3 py-2 border rounded-md\"\n                    >\n                      <option value=\"all\">All Status</option>\n                      <option value=\"pending\">Pending</option>\n                      <option value=\"in_progress\">In Progress</option>\n                      <option value=\"completed\">Completed</option>\n                      <option value=\"review\">In Review</option>\n                    </select>\n                  </div>\n\n                  <DataTable\n                    data={filteredSegments}\n                    columns={columns}\n                    searchable={false}\n                    actions={(row) => (\n                      <>\n                        <Button variant=\"ghost\" size=\"sm\">\n                          <Eye className=\"h-4 w-4\" />\n                        </Button>\n                        <Button variant=\"ghost\" size=\"sm\">\n                          <Edit className=\"h-4 w-4\" />\n                        </Button>\n                        <Button variant=\"ghost\" size=\"sm\">\n                          <MessageSquare className=\"h-4 w-4\" />\n                        </Button>\n                      </>\n                    )}\n                  />\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"available\" className=\"space-y-4\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Available Translation Work</CardTitle>\n                  <CardDescription>\n                    Unassigned segments available for translation\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-center py-8 text-gray-500\">\n                    <FileText className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                    <h3 className=\"text-lg font-medium mb-2\">No available work</h3>\n                    <p className=\"text-sm\">All segments are currently assigned. Check back later for new work.</p>\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"completed\" className=\"space-y-4\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Completed Translations</CardTitle>\n                  <CardDescription>\n                    Your completed translation work\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <DataTable\n                    data={mockSegments.filter(s => s.status === 'completed')}\n                    columns={columns}\n                    searchable={true}\n                    searchPlaceholder=\"Search completed work...\"\n                  />\n                </CardContent>\n              </Card>\n            </TabsContent>\n          </Tabs>\n        </div>\n      </DashboardLayout>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAZA;;;;;;;;;;;;AA0BA,qCAAqC;AACrC,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,aAAa;QACb,UAAU;QACV,eAAe;QACf,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,aAAa;QACb,UAAU;QACV,eAAe;QACf,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,aAAa;QACb,UAAU;QACV,eAAe;QACf,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;CACD;AAED,MAAM,UAAU;IACd;QACE,KAAK;QACL,OAAO;QACP,UAAU;IACZ;IACA;QACE,KAAK;QACL,OAAO;QACP,UAAU;IACZ;IACA;QACE,KAAK;QACL,OAAO;QACP,UAAU;IACZ;IACA;QACE,KAAK;QACL,OAAO;QACP,QAAQ,CAAC,sBACP,8OAAC;gBAAI,WAAU;gBAAoB,OAAO;0BACvC;;;;;;IAGP;IACA;QACE,KAAK;QACL,OAAO;QACP,QAAQ,CAAC,sBACP,8OAAC,2IAAA,CAAA,cAAW;gBAAC,QAAQ;;;;;;IAEzB;IACA;QACE,KAAK;QACL,OAAO;QACP,UAAU;IACZ;IACA;QACE,KAAK;QACL,OAAO;QACP,UAAU;IACZ;IACA;QACE,KAAK;QACL,OAAO;QACP,UAAU;QACV,QAAQ,CAAC,QAAkB,IAAI,KAAK,OAAO,kBAAkB;IAC/D;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,mBAAmB,aAAa,MAAM,CAAC,CAAA;QAC3C,MAAM,gBAAgB,QAAQ,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAClE,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QACvF,MAAM,gBAAgB,mBAAmB,SAAS,QAAQ,MAAM,KAAK;QACrE,OAAO,iBAAiB;IAC1B;IAEA,MAAM,QAAQ;QACZ,OAAO,aAAa,MAAM;QAC1B,SAAS,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAChE,YAAY,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,MAAM;QACvE,WAAW,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QACpE,QAAQ,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;IAChE;IAEA,qBACE;;0BACE,8OAAC,oIAAA,CAAA,UAAO;gBACN,OAAM;gBACN,aAAY;gBACZ,SAAS;;;;;;0BAGX,8OAAC,+IAAA,CAAA,kBAAe;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC,kIAAA,CAAA,SAAM;;sDACL,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAAsB,MAAM,KAAK;;;;;;;;;;;;8DAEhD,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAK1B,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAAoC,MAAM,UAAU;;;;;;;;;;;;8DAEnE,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAKvB,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAAqC,MAAM,SAAS;;;;;;;;;;;;8DAEnE,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAK7B,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAAsC,MAAM,MAAM;;;;;;;;;;;;8DAEjE,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAK7B,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAAoC,MAAM,OAAO;;;;;;;;;;;;8DAEhE,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMzB,8OAAC,gIAAA,CAAA,OAAI;4BAAC,cAAa;4BAAiB,WAAU;;8CAC5C,8OAAC,gIAAA,CAAA,WAAQ;;sDACP,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAiB;;;;;;sDACpC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAY;;;;;;sDAC/B,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAY;;;;;;;;;;;;8CAGjC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAiB,WAAU;8CAC5C,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,8OAAC,gIAAA,CAAA,cAAW;;kEACV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC,iIAAA,CAAA,QAAK;wEACJ,aAAY;wEACZ,OAAO;wEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wEAC9C,WAAU;;;;;;;;;;;;0EAGd,8OAAC;gEACC,OAAO;gEACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gEACjD,WAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAM;;;;;;kFACpB,8OAAC;wEAAO,OAAM;kFAAU;;;;;;kFACxB,8OAAC;wEAAO,OAAM;kFAAc;;;;;;kFAC5B,8OAAC;wEAAO,OAAM;kFAAY;;;;;;kFAC1B,8OAAC;wEAAO,OAAM;kFAAS;;;;;;;;;;;;;;;;;;kEAI3B,8OAAC,yIAAA,CAAA,YAAS;wDACR,MAAM;wDACN,SAAS;wDACT,YAAY;wDACZ,SAAS,CAAC,oBACR;;kFACE,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;kFAEjB,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,8OAAC,2MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASvC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAY,WAAU;8CACvC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAG,WAAU;sEAA2B;;;;;;sEACzC,8OAAC;4DAAE,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM/B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAY,WAAU;8CACvC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,yIAAA,CAAA,YAAS;oDACR,MAAM,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;oDAC5C,SAAS;oDACT,YAAY;oDACZ,mBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtC", "debugId": null}}]}