'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { UserPlus, Loader2, Mail, X } from 'lucide-react';

const inviteTeamMemberSchema = z.object({
  emails: z.array(z.string().email('Invalid email address')).min(1, 'At least one email is required'),
  role: z.enum(['translator', 'reviewer', 'project_manager', 'admin']),
  languages: z.array(z.string()).optional(),
  message: z.string().optional(),
  sendWelcomeEmail: z.boolean().default(true),
});

type InviteTeamMemberFormData = z.infer<typeof inviteTeamMemberSchema>;

interface InviteTeamMemberDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: InviteTeamMemberFormData) => void;
}

const roles = [
  {
    value: 'translator',
    label: 'Translator',
    description: 'Can translate content and manage terminology',
  },
  {
    value: 'reviewer',
    label: 'Reviewer',
    description: 'Can review and approve translations',
  },
  {
    value: 'project_manager',
    label: 'Project Manager',
    description: 'Can manage projects and assign tasks',
  },
  {
    value: 'admin',
    label: 'Administrator',
    description: 'Full access to all features and settings',
  },
];

const languages = [
  'English',
  'Spanish',
  'French',
  'German',
  'Italian',
  'Portuguese',
  'Russian',
  'Chinese (Simplified)',
  'Chinese (Traditional)',
  'Japanese',
  'Korean',
  'Arabic',
  'Dutch',
  'Swedish',
  'Norwegian',
];

export function InviteTeamMemberDialog({ open, onOpenChange, onSubmit }: InviteTeamMemberDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [emailInput, setEmailInput] = useState('');
  const [selectedEmails, setSelectedEmails] = useState<string[]>([]);
  const [selectedLanguages, setSelectedLanguages] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<InviteTeamMemberFormData>({
    resolver: zodResolver(inviteTeamMemberSchema),
    defaultValues: {
      emails: [],
      sendWelcomeEmail: true,
    },
  });

  const role = watch('role');
  const sendWelcomeEmail = watch('sendWelcomeEmail');

  const handleFormSubmit = async (data: InviteTeamMemberFormData) => {
    setIsLoading(true);
    try {
      await onSubmit({
        ...data,
        emails: selectedEmails,
        languages: selectedLanguages,
      });
      reset();
      setSelectedEmails([]);
      setSelectedLanguages([]);
      setEmailInput('');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    setSelectedEmails([]);
    setSelectedLanguages([]);
    setEmailInput('');
    onOpenChange(false);
  };

  const addEmail = () => {
    const email = emailInput.trim();
    if (email && !selectedEmails.includes(email)) {
      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (emailRegex.test(email)) {
        const newEmails = [...selectedEmails, email];
        setSelectedEmails(newEmails);
        setValue('emails', newEmails);
        setEmailInput('');
      }
    }
  };

  const removeEmail = (email: string) => {
    const newEmails = selectedEmails.filter(e => e !== email);
    setSelectedEmails(newEmails);
    setValue('emails', newEmails);
  };

  const handleEmailKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addEmail();
    }
  };

  const toggleLanguage = (language: string) => {
    const newLanguages = selectedLanguages.includes(language)
      ? selectedLanguages.filter(lang => lang !== language)
      : [...selectedLanguages, language];
    setSelectedLanguages(newLanguages);
    setValue('languages', newLanguages);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <UserPlus className="mr-2 h-5 w-5" />
            Invite Team Members
          </DialogTitle>
          <DialogDescription>
            Send invitations to new team members to join your translation workspace.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Email Addresses */}
          <div className="space-y-2">
            <Label>Email Addresses *</Label>
            <div className="flex space-x-2">
              <Input
                placeholder="Enter email address"
                value={emailInput}
                onChange={(e) => setEmailInput(e.target.value)}
                onKeyPress={handleEmailKeyPress}
                disabled={isLoading}
              />
              <Button type="button" onClick={addEmail} disabled={isLoading}>
                Add
              </Button>
            </div>
            
            {selectedEmails.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {selectedEmails.map((email) => (
                  <Badge key={email} variant="secondary" className="flex items-center">
                    <Mail className="mr-1 h-3 w-3" />
                    {email}
                    <button
                      type="button"
                      onClick={() => removeEmail(email)}
                      className="ml-2 text-gray-500 hover:text-gray-700"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
            
            {errors.emails && (
              <p className="text-sm text-red-600">{errors.emails.message}</p>
            )}
          </div>

          {/* Role Selection */}
          <div className="space-y-2">
            <Label>Role *</Label>
            <Select onValueChange={(value) => setValue('role', value as any)}>
              <SelectTrigger>
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                {roles.map((role) => (
                  <SelectItem key={role.value} value={role.value}>
                    <div>
                      <div className="font-medium">{role.label}</div>
                      <div className="text-xs text-gray-500">{role.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.role && (
              <p className="text-sm text-red-600">{errors.role.message}</p>
            )}
          </div>

          {/* Languages (for translators and reviewers) */}
          {(role === 'translator' || role === 'reviewer') && (
            <div className="space-y-2">
              <Label>Languages</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border rounded-md p-3">
                {languages.map((language) => (
                  <div key={language} className="flex items-center space-x-2">
                    <Checkbox
                      id={language}
                      checked={selectedLanguages.includes(language)}
                      onCheckedChange={() => toggleLanguage(language)}
                    />
                    <Label htmlFor={language} className="text-sm">
                      {language}
                    </Label>
                  </div>
                ))}
              </div>
              {selectedLanguages.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {selectedLanguages.map((language) => (
                    <Badge key={language} variant="outline" className="text-xs">
                      {language}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Personal Message */}
          <div className="space-y-2">
            <Label htmlFor="message">Personal Message (Optional)</Label>
            <Textarea
              id="message"
              placeholder="Add a personal message to the invitation..."
              {...register('message')}
              disabled={isLoading}
            />
          </div>

          {/* Send Welcome Email */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="sendWelcomeEmail"
              checked={sendWelcomeEmail}
              onCheckedChange={(checked) => setValue('sendWelcomeEmail', !!checked)}
            />
            <Label htmlFor="sendWelcomeEmail" className="text-sm">
              Send welcome email with getting started guide
            </Label>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading || selectedEmails.length === 0}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Mail className="mr-2 h-4 w-4" />
                  Send Invitations ({selectedEmails.length})
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
