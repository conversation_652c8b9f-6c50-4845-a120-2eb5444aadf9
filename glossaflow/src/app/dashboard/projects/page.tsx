'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal,
  Calendar,
  Users,
  Globe,
  Clock
} from 'lucide-react';

// Mock data
const mockProjects = [
  {
    id: '1',
    name: 'Mobile App Localization',
    description: 'Complete localization of our mobile application for Japanese market',
    status: 'in_progress',
    progress: 75,
    sourceLanguage: 'English',
    targetLanguages: ['Japanese', 'Korean'],
    dueDate: '2024-02-15',
    teamMembers: 4,
    totalSegments: 1250,
    completedSegments: 938,
    createdAt: '2024-01-10',
  },
  {
    id: '2',
    name: 'Website Translation',
    description: 'Marketing website translation for European expansion',
    status: 'review',
    progress: 90,
    sourceLanguage: 'English',
    targetLanguages: ['Spanish', 'French', 'German'],
    dueDate: '2024-02-10',
    teamMembers: 6,
    totalSegments: 850,
    completedSegments: 765,
    createdAt: '2024-01-05',
  },
  {
    id: '3',
    name: 'Documentation Update',
    description: 'Technical documentation translation for product release',
    status: 'completed',
    progress: 100,
    sourceLanguage: 'English',
    targetLanguages: ['French'],
    dueDate: '2024-02-05',
    teamMembers: 2,
    totalSegments: 450,
    completedSegments: 450,
    createdAt: '2024-01-01',
  },
  {
    id: '4',
    name: 'Legal Documents',
    description: 'Translation of legal agreements and terms of service',
    status: 'pending',
    progress: 0,
    sourceLanguage: 'English',
    targetLanguages: ['Spanish', 'Portuguese'],
    dueDate: '2024-03-01',
    teamMembers: 3,
    totalSegments: 320,
    completedSegments: 0,
    createdAt: '2024-01-20',
  },
];

const statusColors = {
  pending: 'bg-gray-100 text-gray-800',
  in_progress: 'bg-blue-100 text-blue-800',
  review: 'bg-yellow-100 text-yellow-800',
  completed: 'bg-green-100 text-green-800',
};

const statusLabels = {
  pending: 'Pending',
  in_progress: 'In Progress',
  review: 'In Review',
  completed: 'Completed',
};

export default function ProjectsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const filteredProjects = mockProjects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || project.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Projects</h1>
            <p className="text-gray-600">Manage your translation projects</p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            New Project
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search projects..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  variant={selectedStatus === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedStatus('all')}
                >
                  All
                </Button>
                <Button
                  variant={selectedStatus === 'in_progress' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedStatus('in_progress')}
                >
                  Active
                </Button>
                <Button
                  variant={selectedStatus === 'review' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedStatus('review')}
                >
                  Review
                </Button>
                <Button
                  variant={selectedStatus === 'completed' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedStatus('completed')}
                >
                  Completed
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredProjects.map((project) => (
            <Card key={project.id} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{project.name}</CardTitle>
                    <CardDescription className="mt-1">
                      {project.description}
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={statusColors[project.status as keyof typeof statusColors]}>
                      {statusLabels[project.status as keyof typeof statusLabels]}
                    </Badge>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Progress */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Progress</span>
                      <span className="text-sm text-gray-600">{project.progress}%</span>
                    </div>
                    <Progress value={project.progress} />
                    <div className="flex items-center justify-between mt-1 text-xs text-gray-500">
                      <span>{project.completedSegments} completed</span>
                      <span>{project.totalSegments} total segments</span>
                    </div>
                  </div>

                  {/* Project Details */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center text-gray-600">
                      <Globe className="mr-2 h-4 w-4" />
                      <span>{project.sourceLanguage} → {project.targetLanguages.join(', ')}</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Users className="mr-2 h-4 w-4" />
                      <span>{project.teamMembers} members</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Calendar className="mr-2 h-4 w-4" />
                      <span>Due {project.dueDate}</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Clock className="mr-2 h-4 w-4" />
                      <span>Created {project.createdAt}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredProjects.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="text-gray-400 mb-4">
                <Search className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery ? 'Try adjusting your search criteria' : 'Get started by creating your first project'}
              </p>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create Project
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}
