'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { 
  ChevronLeft, 
  ChevronRight, 
  Save, 
  MessageSquare, 
  BookOpen,
  Users,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { TranslationEditor } from '@/components/translation/TranslationEditor';
import { TerminologySidebar } from '@/components/translation/TerminologySidebar';
import { CommentsSidebar } from '@/components/translation/CommentsSidebar';

// Mock data
const mockProject = {
  id: '1',
  name: 'Mobile App Localization',
  sourceLanguage: 'English',
  targetLanguage: 'Japanese',
  totalSegments: 1250,
  completedSegments: 938,
};

const mockSegments = [
  {
    id: '1',
    number: 1,
    sourceText: 'Welcome to our mobile application. This app will help you manage your daily tasks efficiently.',
    targetText: '私たちのモバイルアプリケーションへようこそ。このアプリは日常のタスクを効率的に管理するのに役立ちます。',
    status: 'completed',
    comments: 2,
    lastModified: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    number: 2,
    sourceText: 'Create a new task by tapping the plus button in the top right corner.',
    targetText: '右上の角にあるプラスボタンをタップして新しいタスクを作成してください。',
    status: 'completed',
    comments: 0,
    lastModified: '2024-01-15T11:15:00Z',
  },
  {
    id: '3',
    number: 3,
    sourceText: 'You can organize your tasks into different categories for better management.',
    targetText: '',
    status: 'pending',
    comments: 1,
    lastModified: null,
  },
  {
    id: '4',
    number: 4,
    sourceText: 'Set due dates and reminders to never miss important deadlines.',
    targetText: '重要な締切を逃さないように期限とリマインダーを設定してください。',
    status: 'review',
    comments: 3,
    lastModified: '2024-01-15T14:20:00Z',
  },
];

const statusColors = {
  pending: 'bg-gray-100 text-gray-800',
  in_progress: 'bg-blue-100 text-blue-800',
  completed: 'bg-green-100 text-green-800',
  review: 'bg-yellow-100 text-yellow-800',
};

export default function TranslatePage() {
  const params = useParams();
  const projectId = params.id as string;
  
  const [currentSegmentIndex, setCurrentSegmentIndex] = useState(0);
  const [showTerminology, setShowTerminology] = useState(true);
  const [showComments, setShowComments] = useState(false);
  const [isAutoSaving, setIsAutoSaving] = useState(false);

  const currentSegment = mockSegments[currentSegmentIndex];

  const handlePrevious = () => {
    if (currentSegmentIndex > 0) {
      setCurrentSegmentIndex(currentSegmentIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentSegmentIndex < mockSegments.length - 1) {
      setCurrentSegmentIndex(currentSegmentIndex + 1);
    }
  };

  const handleSave = () => {
    setIsAutoSaving(true);
    // Simulate save
    setTimeout(() => {
      setIsAutoSaving(false);
    }, 1000);
  };

  const progress = (mockProject.completedSegments / mockProject.totalSegments) * 100;

  return (
    <DashboardLayout>
      <div className="h-[calc(100vh-8rem)] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-white">
          <div className="flex items-center space-x-4">
            <div>
              <h1 className="text-xl font-semibold">{mockProject.name}</h1>
              <p className="text-sm text-gray-600">
                {mockProject.sourceLanguage} → {mockProject.targetLanguage}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Progress value={progress} className="w-32" />
              <span className="text-sm text-gray-600">
                {mockProject.completedSegments}/{mockProject.totalSegments}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant={showTerminology ? 'default' : 'outline'}
              size="sm"
              onClick={() => setShowTerminology(!showTerminology)}
            >
              <BookOpen className="mr-2 h-4 w-4" />
              Terminology
            </Button>
            <Button
              variant={showComments ? 'default' : 'outline'}
              size="sm"
              onClick={() => setShowComments(!showComments)}
            >
              <MessageSquare className="mr-2 h-4 w-4" />
              Comments
              {currentSegment.comments > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {currentSegment.comments}
                </Badge>
              )}
            </Button>
          </div>
        </div>

        <div className="flex-1 flex">
          {/* Main Translation Area */}
          <div className="flex-1 flex flex-col">
            {/* Segment Navigation */}
            <div className="flex items-center justify-between p-4 border-b bg-gray-50">
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePrevious}
                  disabled={currentSegmentIndex === 0}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <span className="text-sm font-medium">
                  Segment {currentSegment.number} of {mockSegments.length}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNext}
                  disabled={currentSegmentIndex === mockSegments.length - 1}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center space-x-2">
                <Badge className={statusColors[currentSegment.status as keyof typeof statusColors]}>
                  {currentSegment.status.replace('_', ' ')}
                </Badge>
                {currentSegment.lastModified && (
                  <span className="text-xs text-gray-500">
                    Last saved: {new Date(currentSegment.lastModified).toLocaleTimeString()}
                  </span>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSave}
                  disabled={isAutoSaving}
                >
                  {isAutoSaving ? (
                    <>
                      <Clock className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Translation Editor */}
            <div className="flex-1 p-4">
              <TranslationEditor
                sourceText={currentSegment.sourceText}
                targetText={currentSegment.targetText}
                onTargetTextChange={(text) => {
                  // Update segment text
                  console.log('Target text changed:', text);
                }}
                onSave={handleSave}
              />
            </div>

            {/* Segment List */}
            <div className="h-48 border-t bg-gray-50 overflow-y-auto">
              <div className="p-4">
                <h3 className="text-sm font-medium mb-3">All Segments</h3>
                <div className="space-y-2">
                  {mockSegments.map((segment, index) => (
                    <div
                      key={segment.id}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        index === currentSegmentIndex
                          ? 'bg-blue-100 border-blue-200'
                          : 'bg-white hover:bg-gray-50'
                      }`}
                      onClick={() => setCurrentSegmentIndex(index)}
                    >
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">Segment {segment.number}</span>
                        <div className="flex items-center space-x-2">
                          <Badge 
                            size="sm" 
                            className={statusColors[segment.status as keyof typeof statusColors]}
                          >
                            {segment.status}
                          </Badge>
                          {segment.comments > 0 && (
                            <Badge variant="outline" size="sm">
                              {segment.comments} comments
                            </Badge>
                          )}
                        </div>
                      </div>
                      <p className="text-xs text-gray-600 truncate">
                        {segment.sourceText}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Sidebars */}
          {showTerminology && (
            <div className="w-80 border-l">
              <TerminologySidebar
                projectId={projectId}
                sourceText={currentSegment.sourceText}
                onTermSelect={(term) => {
                  console.log('Term selected:', term);
                }}
              />
            </div>
          )}

          {showComments && (
            <div className="w-80 border-l">
              <CommentsSidebar
                segmentId={currentSegment.id}
                comments={[]}
                onAddComment={(comment) => {
                  console.log('Add comment:', comment);
                }}
              />
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
