'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal,
  Mail,
  Phone,
  Globe,
  Calendar,
  BarChart3,
  Edit,
  Trash2,
  UserPlus
} from 'lucide-react';

// Mock team data
const mockTeamMembers = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Lead Translator',
    status: 'active',
    languages: ['English', 'Japanese', 'Korean'],
    specializations: ['Technical', 'Software'],
    joinedAt: '2023-06-15',
    projectsCompleted: 24,
    wordsTranslated: 125000,
    averageRating: 4.9,
    currentProjects: 3,
    avatar: '',
  },
  {
    id: '2',
    name: '<PERSON> <PERSON>',
    email: '<EMAIL>',
    role: 'Senior Reviewer',
    status: 'active',
    languages: ['English', 'Japanese'],
    specializations: ['Legal', 'Business'],
    joinedAt: '2023-03-20',
    projectsCompleted: 18,
    wordsTranslated: 89000,
    averageRating: 4.8,
    currentProjects: 2,
    avatar: '',
  },
  {
    id: '3',
    name: 'Emma Davis',
    email: '<EMAIL>',
    role: 'Project Manager',
    status: 'active',
    languages: ['English'],
    specializations: ['Management', 'Coordination'],
    joinedAt: '2023-01-10',
    projectsCompleted: 45,
    wordsTranslated: 0,
    averageRating: 4.7,
    currentProjects: 8,
    avatar: '',
  },
  {
    id: '4',
    name: 'Alex Kim',
    email: '<EMAIL>',
    role: 'Translator',
    status: 'active',
    languages: ['English', 'Korean', 'Japanese'],
    specializations: ['Marketing', 'Creative'],
    joinedAt: '2023-09-05',
    projectsCompleted: 12,
    wordsTranslated: 67000,
    averageRating: 4.6,
    currentProjects: 2,
    avatar: '',
  },
  {
    id: '5',
    name: 'Lisa Wang',
    email: '<EMAIL>',
    role: 'Translator',
    status: 'inactive',
    languages: ['English', 'Chinese (Simplified)', 'Chinese (Traditional)'],
    specializations: ['Technical', 'Medical'],
    joinedAt: '2023-04-12',
    projectsCompleted: 8,
    wordsTranslated: 34000,
    averageRating: 4.5,
    currentProjects: 0,
    avatar: '',
  },
];

const roleColors = {
  'Lead Translator': 'bg-purple-100 text-purple-800',
  'Senior Reviewer': 'bg-blue-100 text-blue-800',
  'Project Manager': 'bg-green-100 text-green-800',
  'Translator': 'bg-gray-100 text-gray-800',
  'Reviewer': 'bg-yellow-100 text-yellow-800',
};

const statusColors = {
  active: 'bg-green-100 text-green-800',
  inactive: 'bg-gray-100 text-gray-800',
  pending: 'bg-yellow-100 text-yellow-800',
};

export default function TeamPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const filteredMembers = mockTeamMembers.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         member.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesRole = selectedRole === 'all' || member.role === selectedRole;
    const matchesStatus = selectedStatus === 'all' || member.status === selectedStatus;
    return matchesSearch && matchesRole && matchesStatus;
  });

  const roles = [...new Set(mockTeamMembers.map(member => member.role))];
  const statuses = [...new Set(mockTeamMembers.map(member => member.status))];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Team Management</h1>
            <p className="text-gray-600">Manage your translation team members</p>
          </div>
          <Button>
            <UserPlus className="mr-2 h-4 w-4" />
            Invite Member
          </Button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Members</p>
                  <p className="text-2xl font-bold text-gray-900">{mockTeamMembers.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Members</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {mockTeamMembers.filter(m => m.status === 'active').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Globe className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Languages</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {[...new Set(mockTeamMembers.flatMap(m => m.languages))].length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Projects</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {mockTeamMembers.reduce((sum, m) => sum + m.currentProjects, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search team members..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  variant={selectedRole === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedRole('all')}
                >
                  All Roles
                </Button>
                {roles.map(role => (
                  <Button
                    key={role}
                    variant={selectedRole === role ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedRole(role)}
                  >
                    {role}
                  </Button>
                ))}
              </div>
              <div className="flex gap-2">
                <Button
                  variant={selectedStatus === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedStatus('all')}
                >
                  All Status
                </Button>
                {statuses.map(status => (
                  <Button
                    key={status}
                    variant={selectedStatus === status ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedStatus(status)}
                  >
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Members Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredMembers.map((member) => (
            <Card key={member.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={member.avatar} />
                      <AvatarFallback>
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-lg">{member.name}</CardTitle>
                      <p className="text-sm text-gray-600">{member.email}</p>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Mail className="mr-2 h-4 w-4" />
                        Send Message
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Remove
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Badge className={roleColors[member.role as keyof typeof roleColors]}>
                      {member.role}
                    </Badge>
                    <Badge className={statusColors[member.status as keyof typeof statusColors]}>
                      {member.status}
                    </Badge>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">Languages</p>
                    <div className="flex flex-wrap gap-1">
                      {member.languages.map((lang) => (
                        <Badge key={lang} variant="outline" size="sm">
                          {lang}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">Specializations</p>
                    <div className="flex flex-wrap gap-1">
                      {member.specializations.map((spec) => (
                        <Badge key={spec} variant="outline" size="sm">
                          {spec}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="font-medium text-gray-600">Projects</p>
                      <p>{member.projectsCompleted} completed</p>
                      <p>{member.currentProjects} active</p>
                    </div>
                    <div>
                      <p className="font-medium text-gray-600">Performance</p>
                      <p>⭐ {member.averageRating}/5.0</p>
                      <p>{member.wordsTranslated.toLocaleString()} words</p>
                    </div>
                  </div>

                  <div className="text-xs text-gray-500">
                    Joined {member.joinedAt}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredMembers.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <div className="text-gray-400 mb-4">
                <Search className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No team members found</h3>
              <p className="text-gray-600 mb-4">
                Try adjusting your search criteria or invite new members to join your team.
              </p>
              <Button>
                <UserPlus className="mr-2 h-4 w-4" />
                Invite Team Member
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}
