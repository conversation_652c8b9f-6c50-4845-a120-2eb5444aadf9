'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Search, 
  Filter, 
  Download, 
  Upload,
  MoreHorizontal,
  BookOpen,
  Check,
  X,
  Edit
} from 'lucide-react';
import { TerminologyTable } from '@/components/terminology/TerminologyTable';
import { CreateTermDialog } from '@/components/terminology/CreateTermDialog';
import { ImportTerminologyDialog } from '@/components/terminology/ImportTerminologyDialog';

// Mock data
const mockTerminology = [
  {
    id: '1',
    sourceTerm: 'User Interface',
    targetTerm: 'ユーザーインターフェース',
    targetLanguage: 'Japanese',
    category: 'technical',
    context: 'Software development context',
    usageNotes: 'Use this term for all UI-related content',
    approvalStatus: 'approved',
    frequency: 45,
    createdBy: '<PERSON>',
    reviewedBy: '<PERSON>',
    lastUsed: '2024-01-15',
    createdAt: '2024-01-10',
  },
  {
    id: '2',
    sourceTerm: 'Dashboard',
    targetTerm: 'ダッシュボード',
    targetLanguage: 'Japanese',
    category: 'technical',
    context: 'Main application interface',
    usageNotes: 'Preferred over control panel',
    approvalStatus: 'approved',
    frequency: 32,
    createdBy: 'Emma Davis',
    reviewedBy: 'Sarah Chen',
    lastUsed: '2024-01-14',
    createdAt: '2024-01-08',
  },
  {
    id: '3',
    sourceTerm: 'Settings',
    targetTerm: '設定',
    targetLanguage: 'Japanese',
    category: 'general',
    context: 'Configuration options',
    usageNotes: 'Standard translation for settings',
    approvalStatus: 'pending',
    frequency: 28,
    createdBy: 'Alex Kim',
    reviewedBy: null,
    lastUsed: '2024-01-13',
    createdAt: '2024-01-12',
  },
  {
    id: '4',
    sourceTerm: 'Project',
    targetTerm: 'プロジェクト',
    targetLanguage: 'Japanese',
    category: 'general',
    context: 'Translation project context',
    usageNotes: 'Use katakana form',
    approvalStatus: 'approved',
    frequency: 67,
    createdBy: 'Mike Johnson',
    reviewedBy: 'Sarah Chen',
    lastUsed: '2024-01-16',
    createdAt: '2024-01-05',
  },
];

const categories = ['all', 'technical', 'general', 'character', 'location', 'concept'];
const languages = ['Japanese', 'Spanish', 'French', 'German', 'Korean'];
const statuses = ['all', 'approved', 'pending', 'rejected'];

export default function TerminologyPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLanguage, setSelectedLanguage] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);

  const filteredTerminology = mockTerminology.filter(term => {
    const matchesSearch = term.sourceTerm.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         term.targetTerm.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || term.category === selectedCategory;
    const matchesLanguage = selectedLanguage === 'all' || term.targetLanguage === selectedLanguage;
    const matchesStatus = selectedStatus === 'all' || term.approvalStatus === selectedStatus;
    
    return matchesSearch && matchesCategory && matchesLanguage && matchesStatus;
  });

  const stats = {
    total: mockTerminology.length,
    approved: mockTerminology.filter(t => t.approvalStatus === 'approved').length,
    pending: mockTerminology.filter(t => t.approvalStatus === 'pending').length,
    rejected: mockTerminology.filter(t => t.approvalStatus === 'rejected').length,
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Terminology Management</h1>
            <p className="text-gray-600">Manage your translation terminology database</p>
          </div>
          <div className="flex gap-2 mt-4 sm:mt-0">
            <Button variant="outline" onClick={() => setShowImportDialog(true)}>
              <Upload className="mr-2 h-4 w-4" />
              Import
            </Button>
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Term
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BookOpen className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Terms</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Check className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Approved</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.approved}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Filter className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <X className="h-6 w-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Rejected</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.rejected}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search terms..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex flex-wrap gap-2">
                <div className="flex gap-1">
                  <span className="text-sm font-medium text-gray-700 py-2">Category:</span>
                  {categories.map(category => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedCategory(category)}
                    >
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </Button>
                  ))}
                </div>
                
                <div className="flex gap-1">
                  <span className="text-sm font-medium text-gray-700 py-2">Language:</span>
                  <Button
                    variant={selectedLanguage === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedLanguage('all')}
                  >
                    All
                  </Button>
                  {languages.map(language => (
                    <Button
                      key={language}
                      variant={selectedLanguage === language ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedLanguage(language)}
                    >
                      {language}
                    </Button>
                  ))}
                </div>
                
                <div className="flex gap-1">
                  <span className="text-sm font-medium text-gray-700 py-2">Status:</span>
                  {statuses.map(status => (
                    <Button
                      key={status}
                      variant={selectedStatus === status ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedStatus(status)}
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Terminology Table */}
        <TerminologyTable 
          terminology={filteredTerminology}
          onEdit={(term) => console.log('Edit term:', term)}
          onDelete={(termId) => console.log('Delete term:', termId)}
          onApprove={(termId) => console.log('Approve term:', termId)}
          onReject={(termId) => console.log('Reject term:', termId)}
        />

        {/* Dialogs */}
        <CreateTermDialog 
          open={showCreateDialog}
          onOpenChange={setShowCreateDialog}
          onSubmit={(data) => {
            console.log('Create term:', data);
            setShowCreateDialog(false);
          }}
        />
        
        <ImportTerminologyDialog
          open={showImportDialog}
          onOpenChange={setShowImportDialog}
          onImport={(data) => {
            console.log('Import terminology:', data);
            setShowImportDialog(false);
          }}
        />
      </div>
    </DashboardLayout>
  );
}
