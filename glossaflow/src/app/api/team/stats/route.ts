import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Get team members for the user's organization
    const { data: teamMembers, error: teamError } = await supabase
      .from('team_members')
      .select('status, role_name, languages, specializations');

    if (teamError) {
      console.error('Database error:', teamError);
      return NextResponse.json(
        { error: 'Failed to fetch team stats', success: false },
        { status: 500 }
      );
    }

    // Get pending invitations count
    const { count: pendingInvitations, error: invitationError } = await supabase
      .from('team_invitations')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending');

    if (invitationError) {
      console.error('Invitation count error:', invitationError);
    }

    // Calculate statistics
    const totalMembers = teamMembers?.length || 0;
    const activeMembers = teamMembers?.filter(m => m.status === 'active').length || 0;
    const inactiveMembers = teamMembers?.filter(m => m.status === 'inactive').length || 0;

    // Calculate by role
    const byRole: Record<string, number> = {};
    teamMembers?.forEach(member => {
      byRole[member.role_name] = (byRole[member.role_name] || 0) + 1;
    });

    // Calculate by language
    const byLanguage: Record<string, number> = {};
    teamMembers?.forEach(member => {
      member.languages?.forEach((language: string) => {
        byLanguage[language] = (byLanguage[language] || 0) + 1;
      });
    });

    // Calculate by specialization
    const bySpecialization: Record<string, number> = {};
    teamMembers?.forEach(member => {
      member.specializations?.forEach((spec: string) => {
        bySpecialization[spec] = (bySpecialization[spec] || 0) + 1;
      });
    });

    return NextResponse.json({
      success: true,
      data: {
        totalMembers,
        activeMembers,
        inactiveMembers,
        pendingInvitations: pendingInvitations || 0,
        byRole,
        byLanguage,
        bySpecialization,
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
