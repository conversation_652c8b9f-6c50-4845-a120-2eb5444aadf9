import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Get organization ID from session or use a default for now
    const organizationId = session.user.organizationId || null;

    // Fetch project statistics
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('id, status, created_at, updated_at')
      .eq('organization_id', organizationId);

    if (projectsError) {
      console.error('Projects fetch error:', projectsError);
    }

    // Calculate project stats
    const totalProjects = projects?.length || 0;
    const activeProjects = projects?.filter(p => 
      ['planning', 'in_progress', 'review'].includes(p.status)
    ).length || 0;
    const completedProjects = projects?.filter(p => p.status === 'completed').length || 0;
    const pendingProjects = projects?.filter(p => p.status === 'pending').length || 0;

    // Fetch terminology statistics
    const { data: terminology, error: terminologyError } = await supabase
      .from('terminology')
      .select('id, status, created_at')
      .eq('organization_id', organizationId);

    if (terminologyError) {
      console.error('Terminology fetch error:', terminologyError);
    }

    // Calculate terminology stats
    const totalTerms = terminology?.length || 0;
    const approvedTerms = terminology?.filter(t => t.status === 'approved').length || 0;
    const pendingTerms = terminology?.filter(t => t.status === 'pending').length || 0;
    const rejectedTerms = terminology?.filter(t => t.status === 'rejected').length || 0;

    // Fetch team statistics
    const { data: teamMembers, error: teamError } = await supabase
      .from('team_members')
      .select('id, status, created_at')
      .eq('organization_id', organizationId);

    if (teamError) {
      console.error('Team fetch error:', teamError);
    }

    // Calculate team stats
    const totalTeamMembers = teamMembers?.length || 0;
    const activeTeamMembers = teamMembers?.filter(m => m.status === 'active').length || 0;
    const pendingTeamMembers = teamMembers?.filter(m => m.status === 'pending').length || 0;

    // Fetch pending invitations
    const { data: invitations, error: invitationsError } = await supabase
      .from('team_invitations')
      .select('id, status, created_at')
      .eq('organization_id', organizationId)
      .eq('status', 'pending');

    if (invitationsError) {
      console.error('Invitations fetch error:', invitationsError);
    }

    const pendingInvitations = invitations?.length || 0;

    // Calculate recent activity (projects created/updated in last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentProjects = projects?.filter(p => 
      new Date(p.updated_at) > sevenDaysAgo
    ).length || 0;

    const recentTerms = terminology?.filter(t => 
      new Date(t.created_at) > sevenDaysAgo
    ).length || 0;

    // Aggregate dashboard statistics
    const dashboardStats = {
      projects: {
        total: totalProjects,
        active: activeProjects,
        completed: completedProjects,
        pending: pendingProjects,
        recent: recentProjects,
      },
      terminology: {
        total: totalTerms,
        approved: approvedTerms,
        pending: pendingTerms,
        rejected: rejectedTerms,
        recent: recentTerms,
      },
      team: {
        total: totalTeamMembers,
        active: activeTeamMembers,
        pending: pendingTeamMembers,
        invitations: pendingInvitations,
      },
      overview: {
        totalProjects,
        activeProjects,
        totalTerms,
        teamMembers: totalTeamMembers,
        pendingReviews: pendingTerms + pendingProjects,
        recentActivity: recentProjects + recentTerms,
      },
    };

    return NextResponse.json({
      success: true,
      data: dashboardStats,
    });
  } catch (error) {
    console.error('Dashboard stats API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
