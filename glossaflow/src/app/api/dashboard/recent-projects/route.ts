import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '5');

    // Get organization ID from session or use a default for now
    const organizationId = session.user.organizationId || null;

    // Fetch recent projects with basic stats
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select(`
        id,
        name,
        description,
        status,
        source_language,
        target_languages,
        deadline,
        created_at,
        updated_at,
        created_by
      `)
      .eq('organization_id', organizationId)
      .order('updated_at', { ascending: false })
      .limit(limit);

    if (projectsError) {
      console.error('Recent projects fetch error:', projectsError);
      return NextResponse.json(
        { error: 'Failed to fetch recent projects', success: false },
        { status: 500 }
      );
    }

    // Calculate progress for each project (simplified calculation)
    const projectsWithProgress = projects?.map(project => {
      // Simple progress calculation based on status
      let progress = 0;
      switch (project.status) {
        case 'planning':
          progress = 10;
          break;
        case 'in_progress':
          progress = Math.floor(Math.random() * 60) + 20; // 20-80% for demo
          break;
        case 'review':
          progress = Math.floor(Math.random() * 20) + 80; // 80-100% for demo
          break;
        case 'completed':
          progress = 100;
          break;
        case 'on_hold':
          progress = Math.floor(Math.random() * 40) + 10; // 10-50% for demo
          break;
        default:
          progress = 0;
      }

      // Format target languages for display
      const targetLanguage = Array.isArray(project.target_languages) 
        ? project.target_languages[0] 
        : project.target_languages || 'Unknown';

      return {
        id: project.id,
        name: project.name,
        description: project.description,
        status: project.status,
        progress,
        sourceLanguage: project.source_language,
        targetLanguage,
        targetLanguages: project.target_languages,
        deadline: project.deadline,
        createdAt: project.created_at,
        updatedAt: project.updated_at,
        createdBy: project.created_by,
      };
    }) || [];

    return NextResponse.json({
      success: true,
      data: {
        projects: projectsWithProgress,
        total: projectsWithProgress.length,
      },
    });
  } catch (error) {
    console.error('Dashboard recent projects API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
