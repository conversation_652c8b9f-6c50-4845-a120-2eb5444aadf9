import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');

    // Get organization ID from session or use a default for now
    const organizationId = session.user.organizationId || null;

    // Get user information for activity attribution
    const { data: users } = await supabase.auth.admin.listUsers();
    const userMap = new Map();
    users?.users?.forEach(user => {
      userMap.set(user.id, {
        id: user.id,
        name: user.user_metadata?.name || user.user_metadata?.full_name || 'Unknown User',
        email: user.email,
        avatar: user.user_metadata?.avatar_url || user.user_metadata?.picture,
      });
    });

    // Fetch recent project activities
    const { data: recentProjects, error: projectsError } = await supabase
      .from('projects')
      .select('id, name, status, created_at, updated_at, created_by')
      .eq('organization_id', organizationId)
      .order('updated_at', { ascending: false })
      .limit(limit);

    if (projectsError) {
      console.error('Recent projects fetch error:', projectsError);
    }

    // Fetch recent terminology activities
    const { data: recentTerms, error: termsError } = await supabase
      .from('terminology')
      .select('id, term, status, created_at, updated_at, created_by')
      .eq('organization_id', organizationId)
      .order('updated_at', { ascending: false })
      .limit(limit);

    if (termsError) {
      console.error('Recent terms fetch error:', termsError);
    }

    // Fetch recent team activities
    const { data: recentTeamMembers, error: teamError } = await supabase
      .from('team_members')
      .select('id, role_name, status, created_at, updated_at, created_by, user_id')
      .eq('organization_id', organizationId)
      .order('updated_at', { ascending: false })
      .limit(limit);

    if (teamError) {
      console.error('Recent team members fetch error:', teamError);
    }

    // Fetch recent invitations
    const { data: recentInvitations, error: invitationsError } = await supabase
      .from('team_invitations')
      .select('id, email, role_name, status, created_at, updated_at, invited_by')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (invitationsError) {
      console.error('Recent invitations fetch error:', invitationsError);
    }

    // Create activity feed items
    const activities = [];

    // Add project activities
    recentProjects?.forEach(project => {
      const user = userMap.get(project.created_by);
      const isNew = new Date(project.created_at).getTime() === new Date(project.updated_at).getTime();
      
      activities.push({
        id: `project-${project.id}`,
        type: 'project',
        action: isNew ? 'created' : 'updated',
        title: project.name,
        description: isNew 
          ? `${user?.name || 'Someone'} created project "${project.name}"`
          : `Project "${project.name}" status changed to ${project.status}`,
        user: user || { name: 'Unknown User', email: '', avatar: null },
        timestamp: project.updated_at,
        metadata: {
          projectId: project.id,
          status: project.status,
        },
      });
    });

    // Add terminology activities
    recentTerms?.forEach(term => {
      const user = userMap.get(term.created_by);
      const isNew = new Date(term.created_at).getTime() === new Date(term.updated_at).getTime();
      
      activities.push({
        id: `term-${term.id}`,
        type: 'terminology',
        action: isNew ? 'created' : 'updated',
        title: term.term,
        description: isNew 
          ? `${user?.name || 'Someone'} added term "${term.term}"`
          : `Term "${term.term}" status changed to ${term.status}`,
        user: user || { name: 'Unknown User', email: '', avatar: null },
        timestamp: term.updated_at,
        metadata: {
          termId: term.id,
          status: term.status,
        },
      });
    });

    // Add team member activities
    recentTeamMembers?.forEach(member => {
      const user = userMap.get(member.created_by);
      const memberUser = userMap.get(member.user_id);
      const isNew = new Date(member.created_at).getTime() === new Date(member.updated_at).getTime();
      
      activities.push({
        id: `team-${member.id}`,
        type: 'team',
        action: isNew ? 'joined' : 'updated',
        title: `${memberUser?.name || 'Team Member'}`,
        description: isNew 
          ? `${memberUser?.name || 'Someone'} joined as ${member.role_name}`
          : `${memberUser?.name || 'Team member'} status changed to ${member.status}`,
        user: memberUser || { name: 'Unknown User', email: '', avatar: null },
        timestamp: member.updated_at,
        metadata: {
          memberId: member.id,
          role: member.role_name,
          status: member.status,
        },
      });
    });

    // Add invitation activities
    recentInvitations?.forEach(invitation => {
      const user = userMap.get(invitation.invited_by);
      
      activities.push({
        id: `invitation-${invitation.id}`,
        type: 'invitation',
        action: invitation.status === 'pending' ? 'invited' : invitation.status,
        title: invitation.email,
        description: invitation.status === 'pending'
          ? `${user?.name || 'Someone'} invited ${invitation.email} as ${invitation.role_name}`
          : `Invitation to ${invitation.email} was ${invitation.status}`,
        user: user || { name: 'Unknown User', email: '', avatar: null },
        timestamp: invitation.updated_at || invitation.created_at,
        metadata: {
          invitationId: invitation.id,
          email: invitation.email,
          role: invitation.role_name,
          status: invitation.status,
        },
      });
    });

    // Sort activities by timestamp (most recent first)
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Limit to requested number of activities
    const limitedActivities = activities.slice(0, limit);

    return NextResponse.json({
      success: true,
      data: {
        activities: limitedActivities,
        total: activities.length,
      },
    });
  } catch (error) {
    console.error('Dashboard activity API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
