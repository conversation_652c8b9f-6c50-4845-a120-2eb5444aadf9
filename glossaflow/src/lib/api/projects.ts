import { baseApi, ApiResponse, PaginatedResponse } from './base';

export interface Project {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'in_progress' | 'review' | 'completed' | 'on_hold';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  sourceLanguage: string;
  targetLanguages: string[];
  createdAt: string;
  updatedAt: string;
  dueDate?: string;
  budget?: number;
  spent?: number;
  totalSegments: number;
  completedSegments: number;
  reviewedSegments: number;
  approvedSegments: number;
  teamMembers: ProjectMember[];
  files: ProjectFile[];
  createdBy: string;
  organizationId: string;
}

export interface ProjectMember {
  id: string;
  userId: string;
  projectId: string;
  role: 'translator' | 'reviewer' | 'project_manager';
  languages: string[];
  assignedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
}

export interface ProjectFile {
  id: string;
  projectId: string;
  name: string;
  originalName: string;
  size: number;
  type: string;
  url: string;
  segments: number;
  uploadedAt: string;
  uploadedBy: string;
}

export interface CreateProjectRequest {
  name: string;
  description?: string;
  sourceLanguage: string;
  targetLanguages: string[];
  dueDate?: string;
  budget?: number;
  priority: Project['priority'];
  teamMembers?: {
    userId: string;
    role: ProjectMember['role'];
    languages: string[];
  }[];
}

export interface UpdateProjectRequest extends Partial<CreateProjectRequest> {
  status?: Project['status'];
}

export interface ProjectFilters {
  status?: Project['status'];
  priority?: Project['priority'];
  sourceLanguage?: string;
  targetLanguage?: string;
  createdBy?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export const projectsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getProjects: builder.query<PaginatedResponse<Project>, ProjectFilters>({
      query: (filters) => ({
        url: '/projects',
        params: filters,
      }),
      providesTags: ['Project'],
    }),

    getProject: builder.query<ApiResponse<Project>, string>({
      query: (id) => `/projects/${id}`,
      providesTags: (result, error, id) => [{ type: 'Project', id }],
    }),

    createProject: builder.mutation<ApiResponse<Project>, CreateProjectRequest>({
      query: (data) => ({
        url: '/projects',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Project'],
    }),

    updateProject: builder.mutation<ApiResponse<Project>, { id: string; data: UpdateProjectRequest }>({
      query: ({ id, data }) => ({
        url: `/projects/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Project', id }],
    }),

    deleteProject: builder.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `/projects/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Project'],
    }),

    addProjectMember: builder.mutation<ApiResponse<ProjectMember>, {
      projectId: string;
      userId: string;
      role: ProjectMember['role'];
      languages: string[];
    }>({
      query: ({ projectId, ...data }) => ({
        url: `/projects/${projectId}/members`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { projectId }) => [{ type: 'Project', id: projectId }],
    }),

    removeProjectMember: builder.mutation<ApiResponse<void>, {
      projectId: string;
      memberId: string;
    }>({
      query: ({ projectId, memberId }) => ({
        url: `/projects/${projectId}/members/${memberId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { projectId }) => [{ type: 'Project', id: projectId }],
    }),

    uploadProjectFile: builder.mutation<ApiResponse<ProjectFile>, {
      projectId: string;
      file: File;
    }>({
      query: ({ projectId, file }) => {
        const formData = new FormData();
        formData.append('file', file);
        return {
          url: `/projects/${projectId}/files`,
          method: 'POST',
          body: formData,
        };
      },
      invalidatesTags: (result, error, { projectId }) => [{ type: 'Project', id: projectId }],
    }),

    deleteProjectFile: builder.mutation<ApiResponse<void>, {
      projectId: string;
      fileId: string;
    }>({
      query: ({ projectId, fileId }) => ({
        url: `/projects/${projectId}/files/${fileId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { projectId }) => [{ type: 'Project', id: projectId }],
    }),

    getProjectStats: builder.query<ApiResponse<{
      totalProjects: number;
      activeProjects: number;
      completedProjects: number;
      totalSegments: number;
      completedSegments: number;
      totalBudget: number;
      spentBudget: number;
    }>, void>({
      query: () => '/projects/stats',
      providesTags: ['Project'],
    }),
  }),
});

export const {
  useGetProjectsQuery,
  useGetProjectQuery,
  useCreateProjectMutation,
  useUpdateProjectMutation,
  useDeleteProjectMutation,
  useAddProjectMemberMutation,
  useRemoveProjectMemberMutation,
  useUploadProjectFileMutation,
  useDeleteProjectFileMutation,
  useGetProjectStatsQuery,
} = projectsApi;
