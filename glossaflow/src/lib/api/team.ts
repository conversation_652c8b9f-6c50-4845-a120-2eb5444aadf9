import { baseApi, ApiResponse, PaginatedResponse } from './base';

export interface TeamRole {
  id: string;
  name: string;
  description?: string;
  permissions: Record<string, any>;
  isSystemRole: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TeamMember {
  id: string;
  userId: string;
  organizationId: string;
  roleId: string;
  roleName: string;
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  languages: string[];
  specializations: string[];
  bio?: string;
  hourlyRate?: number;
  timezone: string;
  joinedAt: string;
  lastActiveAt?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  // User information (joined from auth.users)
  user?: {
    id: string;
    name?: string;
    email: string;
    avatar?: string;
  };
  // Statistics (joined from team_member_stats)
  stats?: {
    projectsCompleted: number;
    wordsTranslated: number;
    segmentsTranslated: number;
    segmentsReviewed: number;
    averageRating: number;
    currentProjects: number;
    totalHoursWorked: number;
  };
}

export interface TeamInvitation {
  id: string;
  email: string;
  organizationId: string;
  roleId: string;
  roleName: string;
  invitedBy: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  token: string;
  expiresAt: string;
  acceptedAt?: string;
  createdAt: string;
  updatedAt: string;
  // Invited by user info
  invitedByUser?: {
    id: string;
    name?: string;
    email: string;
  };
}

export interface CreateTeamMemberRequest {
  userId: string;
  roleId: string;
  languages?: string[];
  specializations?: string[];
  bio?: string;
  hourlyRate?: number;
  timezone?: string;
}

export interface UpdateTeamMemberRequest extends Partial<CreateTeamMemberRequest> {
  status?: TeamMember['status'];
}

export interface CreateInvitationRequest {
  email: string;
  roleId: string;
  message?: string;
}

export interface TeamFilters {
  search?: string;
  role?: string;
  status?: TeamMember['status'];
  language?: string;
  specialization?: string;
  page?: number;
  limit?: number;
}

export interface TeamStats {
  totalMembers: number;
  activeMembers: number;
  inactiveMembers: number;
  pendingInvitations: number;
  byRole: Record<string, number>;
  byLanguage: Record<string, number>;
  bySpecialization: Record<string, number>;
}

export const teamApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getTeamMembers: builder.query<PaginatedResponse<TeamMember>, TeamFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString());
          }
        });
        return {
          url: `team/members?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) =>
        result?.data.items
          ? [
              ...result.data.items.map(({ id }) => ({ type: 'TeamMember' as const, id })),
              { type: 'TeamMember', id: 'LIST' },
            ]
          : [{ type: 'TeamMember', id: 'LIST' }],
    }),

    getTeamMember: builder.query<ApiResponse<TeamMember>, string>({
      query: (id) => ({
        url: `team/members/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'TeamMember', id }],
    }),

    getTeamStats: builder.query<ApiResponse<TeamStats>, void>({
      query: () => ({
        url: 'team/stats',
        method: 'GET',
      }),
      providesTags: [{ type: 'TeamMember', id: 'STATS' }],
    }),

    getTeamRoles: builder.query<ApiResponse<TeamRole[]>, void>({
      query: () => ({
        url: 'team/roles',
        method: 'GET',
      }),
      providesTags: [{ type: 'TeamRole', id: 'LIST' }],
    }),

    createTeamMember: builder.mutation<ApiResponse<TeamMember>, CreateTeamMemberRequest>({
      query: (data) => ({
        url: 'team/members',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'TeamMember', id: 'LIST' },
        { type: 'TeamMember', id: 'STATS' },
      ],
    }),

    updateTeamMember: builder.mutation<ApiResponse<TeamMember>, {
      id: string;
      data: UpdateTeamMemberRequest;
    }>({
      query: ({ id, data }) => ({
        url: `team/members/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'TeamMember', id },
        { type: 'TeamMember', id: 'LIST' },
        { type: 'TeamMember', id: 'STATS' },
      ],
    }),

    deleteTeamMember: builder.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `team/members/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'TeamMember', id },
        { type: 'TeamMember', id: 'LIST' },
        { type: 'TeamMember', id: 'STATS' },
      ],
    }),

    getTeamInvitations: builder.query<PaginatedResponse<TeamInvitation>, {
      status?: TeamInvitation['status'];
      page?: number;
      limit?: number;
    }>({
      query: (filters = {}) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString());
          }
        });
        return {
          url: `team/invitations?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: [{ type: 'TeamInvitation', id: 'LIST' }],
    }),

    createInvitation: builder.mutation<ApiResponse<TeamInvitation>, CreateInvitationRequest>({
      query: (data) => ({
        url: 'team/invitations',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'TeamInvitation', id: 'LIST' },
        { type: 'TeamMember', id: 'STATS' },
      ],
    }),

    resendInvitation: builder.mutation<ApiResponse<TeamInvitation>, string>({
      query: (id) => ({
        url: `team/invitations/${id}/resend`,
        method: 'POST',
      }),
      invalidatesTags: [{ type: 'TeamInvitation', id: 'LIST' }],
    }),

    cancelInvitation: builder.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `team/invitations/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [
        { type: 'TeamInvitation', id: 'LIST' },
        { type: 'TeamMember', id: 'STATS' },
      ],
    }),

    acceptInvitation: builder.mutation<ApiResponse<TeamMember>, {
      token: string;
      userData?: {
        name: string;
        password: string;
      };
    }>({
      query: (data) => ({
        url: 'team/invitations/accept',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'TeamInvitation', id: 'LIST' },
        { type: 'TeamMember', id: 'LIST' },
        { type: 'TeamMember', id: 'STATS' },
      ],
    }),
  }),
});

export const {
  useGetTeamMembersQuery,
  useGetTeamMemberQuery,
  useGetTeamStatsQuery,
  useGetTeamRolesQuery,
  useCreateTeamMemberMutation,
  useUpdateTeamMemberMutation,
  useDeleteTeamMemberMutation,
  useGetTeamInvitationsQuery,
  useCreateInvitationMutation,
  useResendInvitationMutation,
  useCancelInvitationMutation,
  useAcceptInvitationMutation,
} = teamApi;
