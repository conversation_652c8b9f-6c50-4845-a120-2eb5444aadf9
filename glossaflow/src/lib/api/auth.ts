import { baseApi, ApiResponse } from './base';

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'admin' | 'project_manager' | 'translator' | 'reviewer' | 'client';
  status: 'active' | 'inactive' | 'suspended' | 'pending';
  organizationId: string;
  languages: string[];
  specializations: string[];
  joinedAt: string;
  lastActiveAt?: string;
  preferences: UserPreferences;
}

export interface UserPreferences {
  language: string;
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    projectUpdates: boolean;
    comments: boolean;
    deadlines: boolean;
  };
  editor: {
    fontSize: number;
    theme: 'light' | 'dark';
    autoSave: boolean;
    showTMMatches: boolean;
    showTerminology: boolean;
  };
}

export interface Organization {
  id: string;
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  plan: 'basic' | 'professional' | 'enterprise';
  settings: OrganizationSettings;
  createdAt: string;
  updatedAt: string;
}

export interface OrganizationSettings {
  allowPublicProjects: boolean;
  requireApproval: boolean;
  defaultSourceLanguage: string;
  supportedLanguages: string[];
  qualityThreshold: number;
  autoAssignment: boolean;
}

export interface UpdateProfileRequest {
  name?: string;
  avatar?: string;
  languages?: string[];
  specializations?: string[];
  preferences?: Partial<UserPreferences>;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export const authApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getCurrentUser: builder.query<ApiResponse<User>, void>({
      query: () => '/auth/me',
      providesTags: ['User'],
    }),

    updateProfile: builder.mutation<ApiResponse<User>, UpdateProfileRequest>({
      query: (data) => ({
        url: '/auth/profile',
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['User'],
    }),

    changePassword: builder.mutation<ApiResponse<void>, ChangePasswordRequest>({
      query: (data) => ({
        url: '/auth/change-password',
        method: 'POST',
        body: data,
      }),
    }),

    uploadAvatar: builder.mutation<ApiResponse<{ avatarUrl: string }>, File>({
      query: (file) => {
        const formData = new FormData();
        formData.append('avatar', file);
        return {
          url: '/auth/avatar',
          method: 'POST',
          body: formData,
        };
      },
      invalidatesTags: ['User'],
    }),

    getOrganization: builder.query<ApiResponse<Organization>, void>({
      query: () => '/auth/organization',
      providesTags: ['Organization'],
    }),

    updateOrganization: builder.mutation<ApiResponse<Organization>, Partial<Organization>>({
      query: (data) => ({
        url: '/auth/organization',
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['Organization'],
    }),

    inviteUser: builder.mutation<ApiResponse<void>, {
      email: string;
      role: User['role'];
      languages?: string[];
    }>({
      query: (data) => ({
        url: '/auth/invite',
        method: 'POST',
        body: data,
      }),
    }),

    acceptInvitation: builder.mutation<ApiResponse<User>, {
      token: string;
      name: string;
      password: string;
    }>({
      query: (data) => ({
        url: '/auth/accept-invitation',
        method: 'POST',
        body: data,
      }),
    }),

    refreshToken: builder.mutation<ApiResponse<{ accessToken: string }>, void>({
      query: () => ({
        url: '/auth/refresh',
        method: 'POST',
      }),
    }),

    logout: builder.mutation<ApiResponse<void>, void>({
      query: () => ({
        url: '/auth/logout',
        method: 'POST',
      }),
    }),
  }),
});

export const {
  useGetCurrentUserQuery,
  useUpdateProfileMutation,
  useChangePasswordMutation,
  useUploadAvatarMutation,
  useGetOrganizationQuery,
  useUpdateOrganizationMutation,
  useInviteUserMutation,
  useAcceptInvitationMutation,
  useRefreshTokenMutation,
  useLogoutMutation,
} = authApi;
