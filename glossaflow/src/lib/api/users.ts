import { baseApi, ApiResponse, PaginatedResponse } from './base';
import { User } from './auth';

export interface UserStats {
  projectsCompleted: number;
  wordsTranslated: number;
  averageRating: number;
  currentProjects: number;
  totalEarnings: number;
  productivity: {
    wordsPerHour: number;
    segmentsPerHour: number;
  };
  qualityMetrics: {
    averageScore: number;
    errorRate: number;
    revisionRate: number;
  };
}

export interface UserFilters {
  search?: string;
  role?: User['role'];
  status?: User['status'];
  languages?: string[];
  organizationId?: string;
  page?: number;
  limit?: number;
}

export interface CreateUserRequest {
  name: string;
  email: string;
  role: User['role'];
  languages: string[];
  specializations?: string[];
  organizationId?: string;
}

export interface UpdateUserRequest extends Partial<CreateUserRequest> {
  status?: User['status'];
}

export interface UserActivity {
  id: string;
  userId: string;
  type: 'login' | 'logout' | 'project_created' | 'translation_completed' | 'review_completed';
  description: string;
  metadata?: Record<string, any>;
  timestamp: string;
}

export const usersApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getUsers: builder.query<PaginatedResponse<User>, UserFilters>({
      query: (filters) => ({
        url: '/users',
        params: filters,
      }),
      providesTags: ['User'],
    }),

    getUser: builder.query<ApiResponse<User>, string>({
      query: (id) => `/users/${id}`,
      providesTags: (result, error, id) => [{ type: 'User', id }],
    }),

    createUser: builder.mutation<ApiResponse<User>, CreateUserRequest>({
      query: (data) => ({
        url: '/users',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['User'],
    }),

    updateUser: builder.mutation<ApiResponse<User>, {
      id: string;
      data: UpdateUserRequest;
    }>({
      query: ({ id, data }) => ({
        url: `/users/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'User', id }],
    }),

    deleteUser: builder.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `/users/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['User'],
    }),

    suspendUser: builder.mutation<ApiResponse<User>, string>({
      query: (id) => ({
        url: `/users/${id}/suspend`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, id) => [{ type: 'User', id }],
    }),

    activateUser: builder.mutation<ApiResponse<User>, string>({
      query: (id) => ({
        url: `/users/${id}/activate`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, id) => [{ type: 'User', id }],
    }),

    getUserStats: builder.query<ApiResponse<UserStats>, string>({
      query: (id) => `/users/${id}/stats`,
      providesTags: (result, error, id) => [{ type: 'User', id: `stats-${id}` }],
    }),

    getUserActivity: builder.query<PaginatedResponse<UserActivity>, {
      userId: string;
      page?: number;
      limit?: number;
    }>({
      query: ({ userId, page, limit }) => ({
        url: `/users/${userId}/activity`,
        params: { page, limit },
      }),
      providesTags: (result, error, { userId }) => [{ type: 'User', id: `activity-${userId}` }],
    }),

    getTeamMembers: builder.query<ApiResponse<User[]>, {
      organizationId?: string;
      projectId?: string;
    }>({
      query: (params) => ({
        url: '/users/team',
        params,
      }),
      providesTags: ['User', 'Team'],
    }),

    searchUsers: builder.query<ApiResponse<User[]>, {
      query: string;
      role?: User['role'];
      languages?: string[];
      limit?: number;
    }>({
      query: ({ query, role, languages, limit = 10 }) => ({
        url: '/users/search',
        params: { q: query, role, languages: languages?.join(','), limit },
      }),
      providesTags: ['User'],
    }),

    getUserPerformance: builder.query<ApiResponse<{
      productivity: {
        daily: Array<{ date: string; words: number; segments: number }>;
        weekly: Array<{ week: string; words: number; segments: number }>;
        monthly: Array<{ month: string; words: number; segments: number }>;
      };
      quality: {
        scores: Array<{ date: string; score: number }>;
        trends: {
          improving: boolean;
          averageScore: number;
          changePercent: number;
        };
      };
      projects: Array<{
        id: string;
        name: string;
        role: string;
        progress: number;
        deadline: string;
      }>;
    }>, {
      userId: string;
      period?: 'week' | 'month' | 'quarter' | 'year';
    }>({
      query: ({ userId, period = 'month' }) => ({
        url: `/users/${userId}/performance`,
        params: { period },
      }),
      providesTags: (result, error, { userId }) => [{ type: 'User', id: `performance-${userId}` }],
    }),

    bulkUpdateUsers: builder.mutation<ApiResponse<void>, {
      userIds: string[];
      data: Partial<UpdateUserRequest>;
    }>({
      query: ({ userIds, data }) => ({
        url: '/users/bulk-update',
        method: 'POST',
        body: { userIds, ...data },
      }),
      invalidatesTags: ['User'],
    }),

    exportUsers: builder.mutation<Blob, {
      format: 'csv' | 'excel';
      filters?: UserFilters;
    }>({
      query: ({ format, filters }) => ({
        url: '/users/export',
        method: 'POST',
        body: { format, filters },
        responseHandler: (response) => response.blob(),
      }),
    }),
  }),
});

export const {
  useGetUsersQuery,
  useGetUserQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useSuspendUserMutation,
  useActivateUserMutation,
  useGetUserStatsQuery,
  useGetUserActivityQuery,
  useGetTeamMembersQuery,
  useSearchUsersQuery,
  useGetUserPerformanceQuery,
  useBulkUpdateUsersMutation,
  useExportUsersMutation,
} = usersApi;
