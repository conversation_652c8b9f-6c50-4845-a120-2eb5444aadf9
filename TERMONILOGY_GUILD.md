# Terminology Translation Guide

This document explains how we translate terminology in the Thai Audiobook AI Pipeline for web novels, particularly focusing on martial arts and sci-fi genres.

## Translation Philosophy

Our translation approach balances three key principles:

1. **Cultural Authenticity** - Preserve the original cultural context while making content accessible to Thai readers
2. **Consistency** - Use the same Thai terms for recurring concepts throughout the series
3. **Readability** - Prioritize natural Thai flow over literal translation

## Translation Framework

### 1. Terminology Categories

We organize terminology into structured categories for consistent translation:

- **Core Concepts** (martial arts, sci-fi terms)
- **Character Names** (with proper Thai transliteration)
- **Locations** (places, sects, geographic features)
- **Titles and Ranks** (hierarchy, positions, honorifics)
- **Techniques and Abilities** (combat moves, special powers)
- **Combat Terms** (weapons, actions, battle descriptions)
- **Emotions and Expressions** (character feelings, reactions)
- **Sound Effects** (onomatopoeia, combat sounds)

### 2. Translation Methodology

#### Step 1: Term Identification
- Extract all recurring terms from source material
- Categorize by type (martial arts, sci-fi, character names, etc.)
- Identify context and cultural significance

#### Step 2: Thai Equivalent Research
- Find appropriate Thai translations maintaining cultural context
- Consider existing Thai martial arts/sci-fi terminology
- Ensure terms sound natural in Thai pronunciation

#### Step 3: Consistency Mapping
- Create translation dictionary for each novel
- Document all term variations and contexts
- Establish character-specific language patterns

#### Step 4: Cultural Adaptation
- Adjust honorifics based on Thai social hierarchy
- Modify expressions to match Thai cultural norms
- Preserve original tone while ensuring Thai readability

### 3. Language Style Guidelines

#### Formal vs. Casual Language
- **Formal Thai (ภาษาราชการ)**: Used for sect hierarchy, master-disciple relationships, and ceremonial contexts
- **Casual Thai**: Used for peer interactions and internal thoughts
- **Respectful Language**: Used for addressing superiors with appropriate particles (ครับ/ค่ะ/จ้ะ)

#### Narrative Voice
- **Third-person formal**: Typical of Thai literature
- **Dynamic action sequences**: Vivid Thai verbs and descriptive language
- **Technical explanations**: Simplified while maintaining accuracy

#### Character Dialogue
- **Match personalities**: Formal for masters/elders, casual for peers
- **Status-appropriate pronouns**: เจ้า (casual), ท่าน (respectful), พระองค์ (highly respectful)
- **Natural conversation flow**: Prioritize Thai speech patterns over literal translation

### 4. Specific Translation Rules

#### Character Names
- Use Thai transliteration closest to original pronunciation
- Maintain proper spacing (e.g., "ชุน เยาวุน" not "ชุนเยาวุน")
- Keep consistency across all chapters

#### Martial Arts Terms
- Translate technique names descriptively rather than keeping original language
- Use established Thai martial arts vocabulary when available
- Create compound terms for complex concepts (e.g., "ศิลปะดาบแห่งเทพมาร")

#### Sci-Fi Technology
- Use modern Thai scientific vocabulary
- Maintain technical accuracy while ensuring readability
- Create consistent terminology for recurring concepts

#### Combat Descriptions
- Use dynamic Thai action verbs (โจมตี, ป้องกัน, เฉือน, แทง)
- Translate sound effects with Thai onomatopoeia
- Maintain intensity and excitement of original

### 5. Translation Review Process

Our 6-step review ensures translation quality:

1. **Terminology Consistency**: Verify all terms use established dictionary translations
2. **Narrative Style**: Ensure appropriate Thai web novel voice with engaging, descriptive content
3. **Cultural Authenticity**: Use authentic terminology familiar to Thai readers
4. **Character Voice**: Distinguish personalities through dialogue and speech patterns
5. **Scene Descriptions**: Create vivid, immersive descriptions of combat, locations, and equipment
6. **Dialogue Naturalness**: Ensure conversations flow naturally while maintaining context

### 6. Configuration Management

Each novel maintains its translation configuration in:
```
novels/<novel-id>/config/translation_review.json
```

This file contains:
- Complete terminology dictionary
- Character-specific language patterns
- Translation guidelines
- Style notes for consistency

### 7. Quality Assurance

#### Pre-Translation Checks
- Verify all source terms are documented
- Check for cultural references requiring adaptation
- Identify character relationship dynamics for appropriate language levels

#### Post-Translation Review
- Confirm terminology consistency throughout chapter
- Verify character voice authenticity
- Check narrative flow and readability
- Ensure technical accuracy for specialized terms

#### Final Quality Control
- Cross-reference with established terminology dictionary
- Verify cultural appropriateness of all translations
- Confirm natural Thai language flow
- Test reader comprehension of technical concepts

### 8. Example Applications

#### Martial Arts Context
- **Original**: "Divine Object" → **Thai**: "ของวิเศษ"
- **Context**: Maintains mystical significance while being immediately understandable
- **Usage**: Consistent across all mentions in the series

#### Sci-Fi Technology
- **Original**: "Nano Machine" → **Thai**: "นาโนแมชชีน"
- **Context**: Uses modern Thai scientific vocabulary
- **Usage**: Establishes futuristic tone while remaining accessible

#### Character Dialogue
- **Master to Disciple**: Uses formal language with respectful particles
- **Peer to Peer**: Uses casual language with appropriate familiarity
- **Internal Thoughts**: Uses natural Thai thought patterns

### 9. Common Translation Challenges

#### Cultural References
- Adapt concepts that don't exist in Thai culture
- Maintain original meaning while ensuring comprehension
- Balance authenticity with accessibility

#### Technical Terms
- Simplify complex concepts without losing accuracy
- Create Thai equivalents for foreign technical vocabulary
- Ensure consistency across all technical explanations

#### Emotional Expressions
- Use Thai cultural context for emotional descriptions
- Preserve original intensity while using appropriate Thai expressions
- Match character personality with suitable Thai emotional vocabulary

### 10. Continuous Improvement

Our translation process continuously evolves through:
- Reader feedback integration
- Terminology refinement based on usage patterns
- Cultural sensitivity updates
- Technical accuracy improvements
- Consistency enhancement across series

This systematic approach ensures our Thai audiobook translations maintain high quality while preserving the original work's essence and making it accessible to Thai audiences.